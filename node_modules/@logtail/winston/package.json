{"name": "@logtail/winston", "version": "0.5.5", "description": "Better Stack Winston transport (formerly Logtail)", "keywords": ["Better Stack", "Logtail", "logger", "logging", "node", "winston"], "author": "Better Stack <<EMAIL>>", "homepage": "https://github.com/logtail/logtail-js/tree/master/packages/winston#readme", "license": "ISC", "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/es6/index.d.ts", "sideEffects": false, "files": ["src", "dist"], "repository": {"type": "git", "url": "git+https://github.com/logtail/logtail-js.git"}, "publishConfig": {"access": "public"}, "scripts": {"build:cjs": "tsc", "build:es6": "tsc -p tsconfig.es6.json", "build": "run-p build:*", "prepublishOnly": "npm run build", "test": "echo \"Error: run tests from root\" && exit 1"}, "bugs": {"url": "https://github.com/logtail/logtail-js/issues"}, "devDependencies": {"winston": "^3.2.1"}, "peerDependencies": {"winston": ">=3.2.1"}, "dependencies": {"@logtail/node": "^0.5.5", "@logtail/types": "^0.5.3", "winston-transport": "^4.3.0"}, "gitHead": "30ba39abfa37b728ef32875a50844212d490e73e"}