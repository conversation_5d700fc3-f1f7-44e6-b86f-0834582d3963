var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import Transport from "winston-transport";
const stackContextHint = {
    fileName: "node_modules/winston",
    methodNames: ["log", "error", "warn", "info", "http", "verbose", "debug", "silly"],
};
export class LogtailTransport extends Transport {
    constructor(_logtail, opts) {
        super(Object.assign(Object.assign({}, opts), { close: () => {
                this._logtail.flush().then(() => {
                    if (opts === null || opts === void 0 ? void 0 : opts.close) {
                        opts.close();
                    }
                });
            } }));
        this._logtail = _logtail;
    }
    log(info, cb) {
        // Pass the log to Winston's internal event handlers
        setImmediate(() => {
            this.emit("logged", info);
        });
        const { level, message } = info, meta = __rest(info, ["level", "message"]);
        // Determine the log level
        // Log out to Logtail
        void this._logtail.log(message, level, meta, stackContextHint);
        // Winston callback...
        cb();
    }
}
//# sourceMappingURL=winston.js.map