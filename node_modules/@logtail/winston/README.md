# [Better Stack](https://betterstack.com/logs) JavaScript Winston transport

📣 Logtail is now part of Better Stack. [Learn more ⇗](https://betterstack.com/press/introducing-better-stack/)

[![Better Stack dashboard](https://github.com/logtail/logtail-js/assets/10132717/96b422e7-3026-49c1-bd45-a946c37211d0)](https://betterstack.com/logs)

[![ISC License](https://img.shields.io/badge/license-ISC-ff69b4.svg)](https://github.com/logtail/logtail-js/blob/master/LICENSE.md)
[![npm @logtail/winston](https://img.shields.io/npm/v/@logtail/winston?color=success&label=npm%20%40logtail%2Fwinston)](https://www.npmjs.com/package/@logtail/winston)

Experience SQL-compatible structured log management based on ClickHouse. [Learn more ⇗](https://betterstack.com/logs)

## Documentation

[Getting started ⇗](https://betterstack.com/docs/logs/javascript/winston)

## Need help?

Please let us know at [<EMAIL>](mailto:<EMAIL>). We're happy to help!

---

[ISC license](https://github.com/logtail/logtail-js/blob/master/LICENSE.md), [contributing guidelines](https://github.com/logtail/logtail-js/blob/master/CONTRIBUTING.md).
