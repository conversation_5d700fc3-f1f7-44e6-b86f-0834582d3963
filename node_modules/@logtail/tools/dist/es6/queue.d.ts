/**
 * Queue, for FIFO access to arbitrary objects. Intended to be a faster
 * replacement for a Javascript array.
 */
export default class Queue<T> {
    /**
     * First node in the tree
     */
    private first?;
    /**
     * Last node in the tree
     */
    private last?;
    /**
     * Number of items in the queue
     */
    length: number;
    /**
     * Pushes a value into the queue.
     * @param value - Any object to push into the queue
     */
    push(value: any): void;
    /**
     * Remove a value from the start of the queue (FIFO) and return it
     */
    shift(): T | undefined;
}
