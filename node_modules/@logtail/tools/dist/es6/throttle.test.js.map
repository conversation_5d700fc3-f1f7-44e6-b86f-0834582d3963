{"version": 3, "file": "throttle.test.js", "sourceRoot": "", "sources": ["../../src/throttle.test.ts"], "names": [], "mappings": "AAAA,OAAO,YAAY,MAAM,YAAY,CAAC;AAUtC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;QACxC,WAAW;QACX,MAAM,GAAG,GAAG,CAAC,CAAC;QACd,MAAM,YAAY,GAAG,EAAE,CAAC,CAAC,KAAK;QAC9B,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,YAAY,CAAW,GAAG,CAAC,CAAC;QAE7C,mDAAmD;QACnD,MAAM,QAAQ,GAAG,QAAQ,CACvB,KAAK,EAAE,GAAG,EAAE,EAAE,CACZ,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YAC5B,UAAU,CAAC,GAAG,EAAE;gBACd,OAAO,CAAC,GAAG,CAAC,CAAC;YACf,CAAC,EAAE,YAAY,CAAC,CAAC;QACnB,CAAC,CAAC,CACL,CAAC;QAEF,qBAAqB;QACrB,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,kBAAkB;QAClB,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,mCAAmC;QACnC,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE5B,gBAAgB;QAChB,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAE/C,oEAAoE;QACpE,MAAM,YAAY,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG,YAAY,CAAC;QAC7D,MAAM,qBAAqB,GAAG,GAAG,CAAC;QAElC,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,YAAY,GAAG,qBAAqB,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;QACxC,WAAW;QACX,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAEjC,gBAAgB;QAChB,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,4DAA4D;QAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}