{"version": 3, "file": "retry.test.js", "sourceRoot": "", "sources": ["../../src/retry.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAe,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AACvD,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,SAAS,CAAC;AAEhC;;GAEG;AACH,SAAS,YAAY;IACnB,OAAO;QACL,EAAE,EAAE,IAAI,IAAI,EAAE;QACd,KAAK,EAAE,QAAQ,CAAC,IAAI;QACpB,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;KAC/B,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,MAAgB;IAChC,OAAO,KAAK,UAAU,IAAI,CAAC,IAAmB;QAC5C,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC9C,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;gBACX,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACzB,MAAM,IAAI,GAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAErD,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACzB,MAAM,IAAI,GAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAEhF,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;IAET,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACzB,MAAM,IAAI,GAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAE3G,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;IAET,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACzB,MAAM,IAAI,GAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,oBAAoB,CAAC;aACvB,GAAG,CAAC,GAAG,CAAC;aACR,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC;aACjB,GAAG,CAAC,GAAG,CAAC;aACR,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC;aACjB,GAAG,CAAC,GAAG,CAAC;aACR,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC;aACjB,GAAG,CAAC,GAAG,CAAC;aACR,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAEpB,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAAC,WAAM,CAAC;YACP,MAAM,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC,EAAE,IAAI,CAAC,CAAC;AACX,CAAC,CAAC,CAAC"}