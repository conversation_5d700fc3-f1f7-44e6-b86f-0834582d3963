{"version": 3, "file": "burstProtection.js", "sourceRoot": "", "sources": ["../../src/burstProtection.ts"], "names": [], "mappings": ";;AAUA,sCAkDC;AA1DD,MAAM,UAAU,GAAG,EAAE,CAAC;AAEtB;;;;;GAKG;AACH,SAAwB,mBAAmB,CACzC,YAAoB,EACpB,GAAW,EACX,eAAuB,cAAc;IAErC,IAAI,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;QAClC,OAAO,CAAC,EAAK,EAAE,EAAE,CAAC,EAAE,CAAC;IACvB,CAAC;IAED,IAAI,UAAU,GAAa,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAI,eAAe,GAAW,CAAC,CAAC;IAChC,IAAI,gBAAgB,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;IAE1C,SAAS,gBAAgB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,YAAY,GAAG,UAAU,CAAC;QAEjD,IAAI,GAAG,GAAG,gBAAgB,GAAG,cAAc,EAAE,CAAC;YAC5C,OAAO;QACT,CAAC;QAED,4FAA4F;QAC5F,MAAM,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,cAAc,CAAC,CAAC;QACrF,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QACjH,gBAAgB,IAAI,sBAAsB,GAAG,cAAc,CAAC;IAC9D,CAAC;IAED,SAAS,iBAAiB;QACxB,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,SAAS,kBAAkB;QACzB,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,EAAK,EAAE,EAAE;QACf,OAAO,KAAK,EAAE,GAAG,IAAoB,EAAE,EAAE;YACvC,gBAAgB,EAAE,CAAC;YACnB,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,CAAC;gBAC9B,kBAAkB,EAAE,CAAC;gBACrB,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAC3B,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,eAAe,GAAG,GAAG,GAAG,YAAY,EAAE,CAAC;gBACzC,eAAe,GAAG,GAAG,CAAC;gBACtB,OAAO,CAAC,KAAK,CAAC,GAAG,YAAY,yBAAyB,GAAG,sBAAsB,YAAY,eAAe,CAAC,CAAC;YAC9G,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC"}