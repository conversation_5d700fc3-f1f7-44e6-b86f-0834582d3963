{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../src/retry.ts"], "names": [], "mappings": ";;AAqBA,4BA2CC;AA9DD;;GAEG;AACH,IAAI,SAAS,GAAG,CAAC,CAAC;AAElB;;;;GAIG;AACH,SAAS,KAAK,CAAC,GAAW;IACxB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AACnE,CAAC;AAED;;;;GAIG;AACY,KAAK,UAAU,SAAS,CAAC,EAAmD;IACzF;;OAEG;IACH,IAAI,KAAK,GAAW,CAAC,CAAC;IAEtB;;OAEG;IACH,IAAI,QAAQ,GAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEhC;;;OAGG;IACH,OAAO,KAAK,UAAU,KAAK,CAAC,IAAmB;QAC7C,8CAA8C;QAC9C,OAAO,KAAK,EAAE,GAAG,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH;;mBAEG;gBACH,MAAM,OAAO,GAAG,QAAQ;qBACrB,KAAK,EAAE;qBACP,OAAO,EAAE;qBACT,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;qBACX,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;gBAExC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC;gBACrB,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX;;mBAEG;gBACH,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACxB,MAAM,CAAC,CAAC;gBACV,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC;AACJ,CAAC"}