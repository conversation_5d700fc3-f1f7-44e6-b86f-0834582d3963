import { InferArgs } from "./types";
/**
 * Create a burst protection which allows running function only a number of times in a configurable window
 * @param milliseconds - length of the checked window in milliseconds
 * @param max - maximum number of functions to run in that window
 * @param functionName - function name for error message
 */
export default function makeBurstProtection<T extends (...args: any[]) => any>(milliseconds: number, max: number, functionName?: string): ((fn: T) => T) | ((fn: T) => (...args: InferArgs<T>[]) => Promise<any>);
