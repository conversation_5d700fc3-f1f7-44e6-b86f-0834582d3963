"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const types_1 = require("@logtail/types");
const nock_1 = __importDefault(require("nock"));
const retry_1 = __importDefault(require("./retry"));
/**
 * Create a log with a random string / current date
 */
function getRandomLog() {
    return {
        dt: new Date(),
        level: types_1.LogLevel.Info,
        message: String(Math.random()),
    };
}
function makeSync(called) {
    return async function sync(logs) {
        try {
            const res = await fetch("http://example.com");
            if (res.ok) {
                return Promise.resolve(logs);
            }
            throw new Error("failed!");
        }
        catch (e) {
            called();
            throw e;
        }
    };
}
describe("retry tests", () => {
    it("no failure with correct timing", async () => {
        const called = jest.fn();
        const logs = [getRandomLog()];
        (0, nock_1.default)("http://example.com").get("/").reply(200, logs);
        const sync = makeSync(called);
        const retry = await (0, retry_1.default)(sync);
        const expectedLogs = await retry(logs);
        expect(called).toHaveBeenCalledTimes(0);
    });
    it("one failure with correct timing", async () => {
        const called = jest.fn();
        const logs = [getRandomLog()];
        (0, nock_1.default)("http://example.com").get("/").reply(500, "Bad").get("/").reply(200, logs);
        const sync = makeSync(called);
        const retry = await (0, retry_1.default)(sync);
        const expectedLogs = await retry(logs);
        expect(called).toHaveBeenCalledTimes(1);
    }, 3500);
    it("two failure with correct timing", async () => {
        const called = jest.fn();
        const logs = [getRandomLog()];
        (0, nock_1.default)("http://example.com").get("/").reply(500, "Bad").get("/").reply(500, "Bad").get("/").reply(200, logs);
        const sync = makeSync(called);
        const retry = await (0, retry_1.default)(sync);
        const expectedLogs = await retry(logs);
        expect(called).toHaveBeenCalledTimes(2);
    }, 7000);
    it("three failure with correct timing", async () => {
        const called = jest.fn();
        const logs = [getRandomLog()];
        (0, nock_1.default)("http://example.com")
            .get("/")
            .reply(500, "Bad")
            .get("/")
            .reply(500, "Bad")
            .get("/")
            .reply(500, "Bad")
            .get("/")
            .reply(200, logs);
        const sync = makeSync(called);
        const retry = await (0, retry_1.default)(sync);
        try {
            const expectedLogs = await retry(logs);
        }
        catch (_a) {
            expect(called).toHaveBeenCalledTimes(3);
        }
    }, 7500);
});
//# sourceMappingURL=retry.test.js.map