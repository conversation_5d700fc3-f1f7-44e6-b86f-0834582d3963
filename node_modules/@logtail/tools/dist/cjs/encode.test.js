"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const encode_1 = require("./encode");
describe("Encode function tests", () => {
    // Fixtures
    const ascii = "hello world";
    const base64 = "aGVsbG8gd29ybGQ=";
    it("should convert plain text to base64", () => {
        expect((0, encode_1.base64Encode)(ascii)).toEqual(base64);
    });
});
//# sourceMappingURL=encode.test.js.map