"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateJsonLogSizeBytes = exports.makeThrottle = exports.makeBurstProtection = exports.makeBatch = exports.base64Encode = exports.Queue = void 0;
const queue_1 = __importDefault(require("./queue"));
exports.Queue = queue_1.default;
const encode_1 = require("./encode");
Object.defineProperty(exports, "base64Encode", { enumerable: true, get: function () { return encode_1.base64Encode; } });
const batch_1 = __importStar(require("./batch"));
exports.makeBatch = batch_1.default;
Object.defineProperty(exports, "calculateJsonLogSizeBytes", { enumerable: true, get: function () { return batch_1.calculateJsonLogSizeBytes; } });
const burstProtection_1 = __importDefault(require("./burstProtection"));
exports.makeBurstProtection = burstProtection_1.default;
const throttle_1 = __importDefault(require("./throttle"));
exports.makeThrottle = throttle_1.default;
//# sourceMappingURL=index.js.map