import { ILogtailLog } from "@logtail/types";
export declare const calculateJsonLogSizeBytes: (log: ILogtailLog) => number;
/**
 * batch the buffer coming in, process them and then resolve
 *
 * @param size - Number
 * @param flushTimeout - Number
 * @param retryCount - Number
 * @param retryBackoff - Number
 * @param sizeBytes - Size of the batch (in bytes) that triggers flushing. Set to 0 to disable.
 * @param calculateLogSizeBytes - Function to calculate size of a single ILogtailLog instance (in bytes).
 */
export default function makeBatch(size?: number, flushTimeout?: number, retryCount?: number, retryBackoff?: number, sizeBytes?: number, calculateLogSizeBytes?: (log: ILogtailLog) => number): {
    initPusher: (fn: Function) => (log: ILogtailLog) => Promise<ILogtailLog>;
    flush: () => Promise<void>;
};
