{"name": "@logtail/tools", "version": "0.5.4", "description": "Better Stack JavaScript logging tools (formerly Logtail)", "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/es6/index.d.ts", "sideEffects": false, "files": ["src", "dist"], "publishConfig": {"access": "public"}, "scripts": {"build:cjs": "tsc", "build:es6": "tsc -p tsconfig.es6.json", "build": "run-p build:*", "prepublishOnly": "npm run build", "test": "jest"}, "author": "Better Stack <<EMAIL>>", "repository": "https://github.com/logtail/logtail-js/tree/master/packages/tools", "license": "ISC", "bugs": {"url": "https://github.com/logtail/logtail-js/issues"}, "homepage": "https://github.com/logtail/logtail-js/tree/master/packages/tools#readme", "devDependencies": {"@types/nock": "^11.1.0", "@types/source-map": "^0.5.7", "nock": "^14.0.1"}, "dependencies": {"@logtail/types": "^0.5.3"}, "gitHead": "0f816cacc21b352576a5707741f9151aa1481041"}