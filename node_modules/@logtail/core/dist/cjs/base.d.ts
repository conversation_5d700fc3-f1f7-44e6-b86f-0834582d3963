import { ILog<PERSON>evel, ILogtailLog, ILogtailOptions, Context, Middleware, Sync } from "@logtail/types";
type Message = string | Error;
/**
 * Logtail core class for logging to the Better Stack service
 */
declare class Logtail {
    protected _sourceToken: string;
    protected _options: ILogtailOptions;
    protected _batch: any;
    protected _flush: any;
    protected _logBurstProtection: any;
    protected _middleware: Middleware[];
    protected _sync?: Sync;
    private _countLogged;
    private _countSynced;
    private _countDropped;
    /**
     * Initializes a new Logtail instance
     *
     * @param sourceToken: string - Private source token for logging to Better Stack
     * @param options?: ILogtailOptions - Optionally specify Logtail options
     */
    constructor(sourceToken: string, options?: Partial<ILogtailOptions>);
    /**
     * Flush batched logs to Logtail
     */
    flush(): Promise<any>;
    /**
     * Number of entries logged
     *
     * @returns number
     */
    get logged(): number;
    /**
     * Number of log entries synced with Better Stack
     *
     * @returns number
     */
    get synced(): number;
    /**
     * Number of entries dropped
     *
     * @returns number
     */
    get dropped(): number;
    /**
     * Log an entry, to be synced with Better Stack
     *
     * @param message: string - Log message
     * @param level (LogLevel) - Level to log at (debug|info|warn|error)
     * @param context: (Context) - Context (optional)
     * @returns Promise<ILogtailLog> after syncing
     */
    log<TContext extends Context>(message: Message, level?: ILogLevel, context?: TContext): Promise<ILogtailLog & TContext>;
    private serialize;
    /**
     *
     * Debug level log, to be synced with Better Stack
     *
     * @param message: string - Log message
     * @param context: (Pick<ILogtailLog, "context">) - Context (optional)
     * @returns Promise<ILogtailLog> after syncing
     */
    debug<TContext extends Context>(message: Message, context?: TContext): Promise<ILogtailLog & TContext>;
    /**
     *
     * Info level log, to be synced with Better Stack
     *
     * @param message: string - Log message
     * @param context: (Pick<ILogtailLog, "context">) - Context (optional)
     * @returns Promise<ILogtailLog> after syncing
     */
    info<TContext extends Context>(message: Message, context?: TContext): Promise<ILogtailLog & TContext>;
    /**
     *
     * Warning level log, to be synced with Better Stack
     *
     * @param message: string - Log message
     * @param context: (Pick<ILogtailLog, "context">) - Context (optional)
     * @returns Promise<ILogtailLog> after syncing
     */
    warn<TContext extends Context>(message: Message, context?: TContext): Promise<ILogtailLog & TContext>;
    /**
     *
     * Warning level log, to be synced with Better Stack
     *
     * @param message: string - Log message
     * @param context: (Pick<ILogtailLog, "context">) - Context (optional)
     * @returns Promise<ILogtailLog> after syncing
     */
    error<TContext extends Context>(message: Message, context?: TContext): Promise<ILogtailLog & TContext>;
    /**
     * Sets the sync method - i.e. the final step in the pipeline to get logs
     * over to Better Stack
     *
     * @param fn - Pipeline function to use as sync method
     */
    setSync(fn: Sync): void;
    /**
     * Add a middleware function to the logging pipeline
     *
     * @param fn - Function to add to the log pipeline
     * @returns void
     */
    use(fn: Middleware): void;
    /**
     * Remove a function from the pipeline
     *
     * @param fn - Pipeline function
     * @returns void
     */
    remove(fn: Middleware): void;
}
export default class extends Logtail {
    log<TContext extends Context>(message: Message, level?: ILogLevel, context?: TContext): Promise<ILogtailLog & TContext>;
}
export {};
