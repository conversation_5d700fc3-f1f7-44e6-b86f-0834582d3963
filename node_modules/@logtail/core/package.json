{"name": "@logtail/core", "version": "0.5.4", "description": "Better Stack logging core (formerly Logtail)", "keywords": ["Better Stack", "Logtail", "logger", "logging"], "author": "Better Stack <<EMAIL>>", "homepage": "https://github.com/logtail/logtail-js/tree/master/packages/core#readme", "license": "ISC", "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/es6/index.d.ts", "files": ["src", "dist"], "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/logtail/logtail-js.git"}, "publishConfig": {"access": "public"}, "scripts": {"build:cjs": "tsc", "build:es6": "tsc -p tsconfig.es6.json", "build": "run-p build:*", "prepublishOnly": "npm run build", "test": "echo \"Error: run tests from root\" && exit 1"}, "bugs": {"url": "https://github.com/logtail/logtail-js/issues"}, "dependencies": {"@logtail/tools": "^0.5.4", "@logtail/types": "^0.5.3", "serialize-error": "8.1.0"}, "gitHead": "0f816cacc21b352576a5707741f9151aa1481041"}