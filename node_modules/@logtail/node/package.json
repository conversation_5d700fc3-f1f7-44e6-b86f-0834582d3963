{"name": "@logtail/node", "version": "0.5.5", "description": "Better Stack Node.js logger (formerly Logtail)", "keywords": ["Better Stack", "Logtail", "logger", "logging", "Node"], "author": "Better Stack <<EMAIL>>", "homepage": "https://github.com/logtail/logtail-js/tree/master/packages/node#readme", "license": "ISC", "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/es6/index.d.ts", "files": ["src", "dist"], "repository": {"type": "git", "url": "git+https://github.com/logtail/logtail-js.git"}, "publishConfig": {"access": "public"}, "scripts": {"build:cjs": "tsc", "build:es6": "tsc -p tsconfig.es6.json", "build": "run-p build:*", "prepublishOnly": "npm run build", "test": "echo \"Error: run tests from root\" && exit 1"}, "bugs": {"url": "https://github.com/logtail/logtail-js/issues"}, "devDependencies": {"@types/fetch-mock": "^7.3.1", "@types/nock": "^11.1.0", "nock": "^14.0.1"}, "dependencies": {"@logtail/core": "^0.5.4", "@logtail/types": "^0.5.3", "@msgpack/msgpack": "^2.5.1", "@types/stack-trace": "^0.0.33", "minimatch": "^9.0.5", "stack-trace": "0.0.10"}, "gitHead": "30ba39abfa37b728ef32875a50844212d490e73e"}