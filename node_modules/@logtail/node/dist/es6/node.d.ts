import { Duplex, Writable } from "stream";
import { Context, ILog<PERSON>evel, ILog<PERSON>Log, ILogtailNodeOptions, StackContextHint } from "@logtail/types";
import { Base } from "@logtail/core";
export declare class Node extends Base {
    /**
     * Readable/Duplex stream where <PERSON><PERSON><PERSON> stringified logs of type `ILogtailLog`
     * will be pushed after syncing
     */
    private _writeStream?;
    constructor(sourceToken: string, options?: Partial<ILogtailNodeOptions>);
    /**
     * Override `Base` log to enable Node.js streaming
     *
     * @param message: string - Log message
     * @param level (LogLevel) - Level to log at (debug|info|warn|error)
     * @param context: (Context) - Log context for passing structured data
     * @param stackContextHint: (StackContextHint|null) - Info about which methods to consider as origin in context.runtime
     * @returns Promise<ILogtailLog> after syncing
     */
    log<TContext extends Context>(message: string, level?: ILogLevel, context?: TContext, stackContextHint?: StackContextHint): Promise<ILogtailLog & TContext>;
    /**
     * Pi<PERSON> stringified `ILogtailLog` to a stream after syncing
     *
     * @param stream - Writable|Duplex stream
     */
    pipe(stream: Writable | Duplex): Writable | Duplex;
    private encodeAsMsgpack;
    private createAgent;
    private getHttpModule;
}
