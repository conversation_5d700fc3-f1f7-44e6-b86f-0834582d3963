{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../../src/context.ts"], "names": [], "mappings": ";;;;;AAYA,0CAoBC;AA/BD,+BAAyC;AACzC,8DAAqD;AAGrD,MAAM,QAAQ,GAAG,YAAY,EAAE,CAAC;AAChC;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,OAAa,EAAE,gBAAmC;IAChF,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAC9D,IAAI,UAAU,KAAK,IAAI;QAAE,OAAO,EAAE,CAAC;IAEnC,OAAO;QACL,OAAO,EAAE;YACP,OAAO,EAAE;gBACP,IAAI,EAAE,oBAAoB,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBACpD,IAAI,EAAE,UAAU,CAAC,WAAW,EAAE;gBAC9B,MAAM,EAAE,UAAU,CAAC,aAAa,EAAE;gBAClC,QAAQ,EAAE,UAAU,CAAC,eAAe,EAAE;gBACtC,IAAI,EAAE,UAAU,CAAC,aAAa,EAAE;gBAChC,MAAM,EAAE,UAAU,CAAC,eAAe,EAAE;aACrC;YACD,MAAM,EAAE;gBACN,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,SAAS,EAAE,QAAQ;aACpB;SACF;KACF,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,OAAa,EAAE,gBAAmC;IACzE,KAAK,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvF,MAAM,KAAK,GAAG,qBAAU,CAAC,GAAG,CAAC,EAAS,CAAC,CAAC;QACxC,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,IAAG,CAAC;YAAE,OAAO,qBAAqB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC/E,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAoB,EAAE,gBAAmC;IACtF,IAAI,gBAAgB,EAAE,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;QACjB,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;;YACrC,OAAO,CACL,CAAA,MAAA,KAAK,CAAC,WAAW,EAAE,0CAAE,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBACxD,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;oBAC3D,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,CAClE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,OAAO,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAgB;IAC5C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9B,OAAO,GAAG,CAAC,QAAQ,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,MAAM,QAAQ,GAAG,IAAA,cAAO,EAAC,YAAY,EAAE,CAAC,CAAC;QACzC,OAAO,IAAA,eAAQ,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;AACH,CAAC;AAED,SAAS,YAAY;IACnB,IAAI,IAAI,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAC;IACzB,IAAI,IAAI,KAAK,SAAS;QAAE,OAAO,EAAE,CAAC;IAClC,oDAAoD;IACpD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACnD,wBAAwB;YACxB,MAAM;QACR,CAAC;QACD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,CAAC;QACb,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC"}