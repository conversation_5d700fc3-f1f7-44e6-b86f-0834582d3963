{"name": "@logtail/types", "version": "0.5.3", "description": "Better Stack Typescript types (formerly Logtail)", "keywords": ["Better Stack", "Logtail", "logging", "logger", "typescript", "types"], "author": "Better Stack <<EMAIL>>", "homepage": "https://github.com/logtail/logtail-js/tree/master/packages/types#readme", "license": "ISC", "main": "dist/cjs/types.js", "module": "dist/es6/types.js", "types": "dist/es6/types.d.ts", "sideEffects": false, "files": ["src", "dist"], "repository": {"type": "git", "url": "git+https://github.com/logtail/logtail-js.git"}, "publishConfig": {"access": "public"}, "scripts": {"build:cjs": "tsc", "build:es6": "tsc -p tsconfig.es6.json", "build": "run-p build:*", "prepublishOnly": "npm run build", "test": "echo \"Error: run tests from root\" && exit 1"}, "bugs": {"url": "https://github.com/logtail/logtail-js/issues"}, "devDependencies": {"npm-run-all": "^4.1.5"}, "gitHead": "4e656e66560fa1a16f82bbb12df7ba6769ed15eb"}