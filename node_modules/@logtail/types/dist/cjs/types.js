"use strict";
/**
 * Logtail library options
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    // core log levels - available as functions
    LogLevel["Error"] = "error";
    LogLevel["Warn"] = "warn";
    LogLevel["Info"] = "info";
    LogLevel["Debug"] = "debug";
    // extra log levels - recognized when passed from logging frameworks
    LogLevel["Fatal"] = "fatal";
    LogLevel["Http"] = "http";
    LogLevel["Verbose"] = "verbose";
    LogLevel["Silly"] = "silly";
    LogLevel["Trace"] = "trace";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
//# sourceMappingURL=types.js.map