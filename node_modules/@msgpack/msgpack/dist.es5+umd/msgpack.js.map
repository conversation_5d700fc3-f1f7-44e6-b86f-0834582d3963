{"version": 3, "file": "msgpack.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;UCVA;UACA;;;;;WCDA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA,kBAAkB;AAEX,IAAM,UAAU,GAAG,UAAW,CAAC;AAEtC,+CAA+C;AAC/C,kEAAkE;AAE3D,SAAS,SAAS,CAAC,IAAc,EAAE,MAAc,EAAE,KAAa;IACrE,IAAM,IAAI,GAAG,KAAK,GAAG,UAAa,CAAC;IACnC,IAAM,GAAG,GAAG,KAAK,CAAC,CAAC,sCAAsC;IACzD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAEM,SAAS,QAAQ,CAAC,IAAc,EAAE,MAAc,EAAE,KAAa;IACpE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAa,CAAC,CAAC;IAC/C,IAAM,GAAG,GAAG,KAAK,CAAC,CAAC,sCAAsC;IACzD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAEM,SAAS,QAAQ,CAAC,IAAc,EAAE,MAAc;IACrD,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnC,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,OAAO,IAAI,GAAG,UAAa,GAAG,GAAG,CAAC;AACpC,CAAC;AAEM,SAAS,SAAS,CAAC,IAAc,EAAE,MAAc;IACtD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpC,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,OAAO,IAAI,GAAG,UAAa,GAAG,GAAG,CAAC;AACpC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BD,gEAAgE;AAC7B;AAEnC,IAAM,uBAAuB,GAC3B,CAAC,OAAO,OAAO,KAAK,WAAW,IAAI,cAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAG,eAAe,CAAC,MAAK,OAAO,CAAC;IAC/E,OAAO,WAAW,KAAK,WAAW;IAClC,OAAO,WAAW,KAAK,WAAW,CAAC;AAE9B,SAAS,SAAS,CAAC,GAAW;IACnC,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;IAE7B,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,OAAO,GAAG,GAAG,SAAS,EAAE;QACtB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE;YAC9B,SAAS;YACT,UAAU,EAAE,CAAC;YACb,SAAS;SACV;aAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE;YACrC,UAAU;YACV,UAAU,IAAI,CAAC,CAAC;SACjB;aAAM;YACL,wBAAwB;YACxB,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE;gBACtC,iBAAiB;gBACjB,IAAI,GAAG,GAAG,SAAS,EAAE;oBACnB,IAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE;wBAC/B,EAAE,GAAG,CAAC;wBACN,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;qBAC7D;iBACF;aACF;YAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE;gBAC9B,SAAS;gBACT,UAAU,IAAI,CAAC,CAAC;aACjB;iBAAM;gBACL,SAAS;gBACT,UAAU,IAAI,CAAC,CAAC;aACjB;SACF;KACF;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAEM,SAAS,YAAY,CAAC,GAAW,EAAE,MAAkB,EAAE,YAAoB;IAChF,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI,MAAM,GAAG,YAAY,CAAC;IAC1B,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,OAAO,GAAG,GAAG,SAAS,EAAE;QACtB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE;YAC9B,SAAS;YACT,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC;YACzB,SAAS;SACV;aAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE;YACrC,UAAU;YACV,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;SACjD;aAAM;YACL,wBAAwB;YACxB,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE;gBACtC,iBAAiB;gBACjB,IAAI,GAAG,GAAG,SAAS,EAAE;oBACnB,IAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE;wBAC/B,EAAE,GAAG,CAAC;wBACN,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;qBAC7D;iBACF;aACF;YAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE;gBAC9B,SAAS;gBACT,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;aACjD;iBAAM;gBACL,SAAS;gBACT,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;aACjD;SACF;QAED,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;KAC1C;AACH,CAAC;AAED,IAAM,iBAAiB,GAAG,uBAAuB,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3E,IAAM,sBAAsB,GAAG,CAAC,uBAAuB;IAC5D,CAAC,CAAC,UAAU;IACZ,CAAC,CAAC,OAAO,OAAO,KAAK,WAAW,IAAI,cAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAG,eAAe,CAAC,MAAK,OAAO;QAC/E,CAAC,CAAC,GAAG;QACL,CAAC,CAAC,CAAC,CAAC;AAEN,SAAS,kBAAkB,CAAC,GAAW,EAAE,MAAkB,EAAE,YAAoB;IAC/E,MAAM,CAAC,GAAG,CAAC,iBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAW,EAAE,MAAkB,EAAE,YAAoB;IACnF,iBAAkB,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;AACpE,CAAC;AAEM,IAAM,YAAY,GAAG,kBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,UAAU,EAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,kBAAkB,CAAC;AAExG,IAAM,UAAU,GAAG,IAAO,CAAC;AAEpB,SAAS,YAAY,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;IACrF,IAAI,MAAM,GAAG,WAAW,CAAC;IACzB,IAAM,GAAG,GAAG,MAAM,GAAG,UAAU,CAAC;IAEhC,IAAM,KAAK,GAAkB,EAAE,CAAC;IAChC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,OAAO,MAAM,GAAG,GAAG,EAAE;QACnB,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,SAAS;YACT,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnB;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAClC,UAAU;YACV,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;SAC3C;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAClC,UAAU;YACV,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;SAC3D;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAClC,UAAU;YACV,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC;YAChF,IAAI,IAAI,GAAG,MAAM,EAAE;gBACjB,IAAI,IAAI,OAAO,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC7C,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;aAChC;YACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClB;aAAM;YACL,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnB;QAED,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,EAAE;YAC9B,MAAM,IAAI,MAAM,CAAC,YAAY,OAAnB,MAAM,2BAAiB,KAAK,UAAC,CAAC;YACxC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;SAClB;KACF;IAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACpB,MAAM,IAAI,MAAM,CAAC,YAAY,OAAnB,MAAM,2BAAiB,KAAK,UAAC,CAAC;KACzC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,IAAM,iBAAiB,GAAG,uBAAuB,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;AACtE,IAAM,sBAAsB,GAAG,CAAC,uBAAuB;IAC5D,CAAC,CAAC,UAAU;IACZ,CAAC,CAAC,OAAO,OAAO,KAAK,WAAW,IAAI,cAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAG,cAAc,CAAC,MAAK,OAAO;QAC9E,CAAC,CAAC,GAAG;QACL,CAAC,CAAC,CAAC,CAAC;AAEC,SAAS,YAAY,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;IACrF,IAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,GAAG,UAAU,CAAC,CAAC;IAC1E,OAAO,iBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAChD,CAAC;;;ACzKD;;GAEG;AACH;IACE,iBAAqB,IAAY,EAAW,IAAgB;QAAvC,SAAI,GAAJ,IAAI,CAAQ;QAAW,SAAI,GAAJ,IAAI,CAAY;IAAG,CAAC;IAClE,cAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;ACLD;IAAiC,+BAAK;IACpC,qBAAY,OAAe;QAA3B,YACE,kBAAM,OAAO,CAAC,SAWf;QATC,kDAAkD;QAClD,IAAM,KAAK,GAAiC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACjF,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,KAAK,CAAC,CAAC;QAEnC,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,MAAM,EAAE;YAClC,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,WAAW,CAAC,IAAI;SACxB,CAAC,CAAC;;IACL,CAAC;IACH,kBAAC;AAAD,CAAC,CAdgC,KAAK,GAcrC;;;;ACdD,kFAAkF;AACtC;AACK;AAE1C,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC;AAOhC,IAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AACnE,IAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AAE5D,SAAS,yBAAyB,CAAC,EAAuB;QAArB,GAAG,WAAE,IAAI;IACnD,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE;QACvD,6BAA6B;QAC7B,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE;YAC5C,sCAAsC;YACtC,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACvB,OAAO,EAAE,CAAC;SACX;aAAM;YACL,yDAAyD;YACzD,IAAM,OAAO,GAAG,GAAG,GAAG,WAAW,CAAC;YAClC,IAAM,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC;YAChC,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,oBAAoB;YACpB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC;YACjD,WAAW;YACX,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,OAAO,EAAE,CAAC;SACX;KACF;SAAM;QACL,uDAAuD;QACvD,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACxB,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACvB,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAEM,SAAS,oBAAoB,CAAC,IAAU;IAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACnC,IAAM,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAEtC,uDAAuD;IACvD,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACzC,OAAO;QACL,GAAG,EAAE,GAAG,GAAG,SAAS;QACpB,IAAI,EAAE,IAAI,GAAG,SAAS,GAAG,GAAG;KAC7B,CAAC;AACJ,CAAC;AAEM,SAAS,wBAAwB,CAAC,MAAe;IACtD,IAAI,MAAM,YAAY,IAAI,EAAE;QAC1B,IAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,yBAAyB,CAAC,QAAQ,CAAC,CAAC;KAC5C;SAAM;QACL,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAEM,SAAS,yBAAyB,CAAC,IAAgB;IACxD,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAEzE,iCAAiC;IACjC,QAAQ,IAAI,CAAC,UAAU,EAAE;QACvB,KAAK,CAAC,CAAC,CAAC;YACN,2BAA2B;YAC3B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAM,IAAI,GAAG,CAAC,CAAC;YACf,OAAO,EAAE,GAAG,OAAE,IAAI,QAAE,CAAC;SACtB;QACD,KAAK,CAAC,CAAC,CAAC;YACN,mCAAmC;YACnC,IAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,IAAM,GAAG,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC;YAC/D,IAAM,IAAI,GAAG,iBAAiB,KAAK,CAAC,CAAC;YACrC,OAAO,EAAE,GAAG,OAAE,IAAI,QAAE,CAAC;SACtB;QACD,KAAK,EAAE,CAAC,CAAC;YACP,uDAAuD;YAEvD,IAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO,EAAE,GAAG,OAAE,IAAI,QAAE,CAAC;SACtB;QACD;YACE,MAAM,IAAI,WAAW,CAAC,uEAAgE,IAAI,CAAC,MAAM,CAAE,CAAC,CAAC;KACxG;AACH,CAAC;AAEM,SAAS,wBAAwB,CAAC,IAAgB;IACvD,IAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACjD,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC5D,CAAC;AAEM,IAAM,kBAAkB,GAAG;IAChC,IAAI,EAAE,aAAa;IACnB,MAAM,EAAE,wBAAwB;IAChC,MAAM,EAAE,wBAAwB;CACjC,CAAC;;;AC3GF,kDAAkD;AAEd;AACa;AAkBjD;IAgBE;QARA,sBAAsB;QACL,oBAAe,GAAgE,EAAE,CAAC;QAClF,oBAAe,GAAgE,EAAE,CAAC;QAEnG,oBAAoB;QACH,aAAQ,GAAgE,EAAE,CAAC;QAC3E,aAAQ,GAAgE,EAAE,CAAC;QAG1F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IACpC,CAAC;IAEM,iCAAQ,GAAf,UAAgB,EAQf;YAPC,IAAI,YACJ,MAAM,cACN,MAAM;QAMN,IAAI,IAAI,IAAI,CAAC,EAAE;YACb,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;SAC9B;aAAM;YACL,sBAAsB;YACtB,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;SACtC;IACH,CAAC;IAEM,oCAAW,GAAlB,UAAmB,MAAe,EAAE,OAAoB;QACtD,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpD,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACpB,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBAChC;aACF;SACF;QAED,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,IAAM,IAAI,GAAG,CAAC,CAAC;oBACf,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBAChC;aACF;SACF;QAED,IAAI,MAAM,YAAY,OAAO,EAAE;YAC7B,wBAAwB;YACxB,OAAO,MAAM,CAAC;SACf;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,+BAAM,GAAb,UAAc,IAAgB,EAAE,IAAY,EAAE,OAAoB;QAChE,IAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,SAAS,EAAE;YACb,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACvC;aAAM;YACL,mDAAmD;YACnD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAChC;IACH,CAAC;IAhFsB,2BAAY,GAAkC,IAAI,cAAc,EAAE,CAAC;IAiF5F,qBAAC;CAAA;AAlF0B;;;ACrBpB,SAAS,gBAAgB,CAAC,MAAsE;IACrG,IAAI,MAAM,YAAY,UAAU,EAAE;QAChC,OAAO,MAAM,CAAC;KACf;SAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QACrC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;KAC5E;SAAM,IAAI,MAAM,YAAY,WAAW,EAAE;QACxC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;KAC/B;SAAM;QACL,oBAAoB;QACpB,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAChC;AACH,CAAC;AAEM,SAAS,cAAc,CAAC,MAAyD;IACtF,IAAI,MAAM,YAAY,WAAW,EAAE;QACjC,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC7B;IAED,IAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC5C,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;AACvF,CAAC;;;;;;;;;;;;;;ACpB4F;AACvB;AACpB;AACK;AAGhD,IAAM,iBAAiB,GAAG,GAAG,CAAC;AAC9B,IAAM,2BAA2B,GAAG,IAAI,CAAC;AAEhD;IAKE,iBACmB,cAAoF,EACpF,OAAuC,EACvC,QAA4B,EAC5B,iBAA+C,EAC/C,QAAgB,EAChB,YAAoB,EACpB,eAAuB,EACvB,mBAA2B;QAP3B,kDAAkD,2BAAkC;QACpF,oCAAuB,SAAgB;QACvC,uDAA4B;QAC5B,mFAA+C;QAC/C,2CAAgB;QAChB,mDAAoB;QACpB,yDAAuB;QACvB,iEAA2B;QAP3B,mBAAc,GAAd,cAAc,CAAsE;QACpF,YAAO,GAAP,OAAO,CAAgC;QACvC,aAAQ,GAAR,QAAQ,CAAoB;QAC5B,sBAAiB,GAAjB,iBAAiB,CAA8B;QAC/C,aAAQ,GAAR,QAAQ,CAAQ;QAChB,iBAAY,GAAZ,YAAY,CAAQ;QACpB,oBAAe,GAAf,eAAe,CAAQ;QACvB,wBAAmB,GAAnB,mBAAmB,CAAQ;QAZtC,QAAG,GAAG,CAAC,CAAC;QACR,SAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAC7D,UAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAW9C,CAAC;IAEI,mCAAiB,GAAzB;QACE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,iCAAe,GAAtB,UAAuB,MAAe;QACpC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,wBAAM,GAAb,UAAc,MAAe;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,MAAe,EAAE,KAAa;QAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,oCAA6B,KAAK,CAAE,CAAC,CAAC;SACvD;QAED,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;aAAM,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;YACtC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;SAC5B;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;SAC3B;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;SAC3B;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAClC;IACH,CAAC;IAEO,yCAAuB,GAA/B,UAAgC,WAAmB;QACjD,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;QAE5C,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,YAAY,EAAE;YACvC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;SACrC;IACH,CAAC;IAEO,8BAAY,GAApB,UAAqB,OAAe;QAClC,IAAM,SAAS,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAM,OAAO,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC;QAExC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACxB,CAAC;IAEO,2BAAS,GAAjB;QACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAEO,+BAAa,GAArB,UAAsB,MAAe;QACnC,IAAI,MAAM,KAAK,KAAK,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;IACH,CAAC;IACO,8BAAY,GAApB,UAAqB,MAAc;QACjC,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7D,IAAI,MAAM,IAAI,CAAC,EAAE;gBACf,IAAI,MAAM,GAAG,IAAI,EAAE;oBACjB,kBAAkB;oBAClB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACtB;qBAAM,IAAI,MAAM,GAAG,KAAK,EAAE;oBACzB,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACtB;qBAAM,IAAI,MAAM,GAAG,OAAO,EAAE;oBAC3B,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACvB;qBAAM,IAAI,MAAM,GAAG,WAAW,EAAE;oBAC/B,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACvB;qBAAM;oBACL,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACvB;aACF;iBAAM;gBACL,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE;oBACnB,kBAAkB;oBAClB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;iBACtC;qBAAM,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE;oBAC1B,QAAQ;oBACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACtB;qBAAM,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;oBAC5B,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACvB;qBAAM,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE;oBAChC,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACvB;qBAAM;oBACL,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACvB;aACF;SACF;aAAM;YACL,sBAAsB;YACtB,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,WAAW;gBACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACvB;iBAAM;gBACL,WAAW;gBACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACvB;SACF;IACH,CAAC;IAEO,mCAAiB,GAAzB,UAA0B,UAAkB;QAC1C,IAAI,UAAU,GAAG,EAAE,EAAE;YACnB,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC;SACjC;aAAM,IAAI,UAAU,GAAG,KAAK,EAAE;YAC7B,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC1B;aAAM,IAAI,UAAU,GAAG,OAAO,EAAE;YAC/B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC3B;aAAM,IAAI,UAAU,GAAG,WAAW,EAAE;YACnC,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC3B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,2BAAoB,UAAU,oBAAiB,CAAC,CAAC;SAClE;IACH,CAAC;IAEO,8BAAY,GAApB,UAAqB,MAAc;QACjC,IAAM,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;QAEhC,IAAI,SAAS,GAAG,sBAAsB,EAAE;YACtC,IAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,uBAAuB,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC;SACxB;aAAM;YACL,IAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,uBAAuB,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC;SACxB;IACH,CAAC;IAEO,8BAAY,GAApB,UAAqB,MAAe,EAAE,KAAa;QACjD,kEAAkE;QAClE,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAChC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACjC;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;SAC3B;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YACrC,IAAI,CAAC,SAAS,CAAC,MAAiC,EAAE,KAAK,CAAC,CAAC;SAC1D;aAAM;YACL,0FAA0F;YAC1F,MAAM,IAAI,KAAK,CAAC,+BAAwB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAE,CAAC,CAAC;SACpF;IACH,CAAC;IAEO,8BAAY,GAApB,UAAqB,MAAuB;QAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;QAC/B,IAAI,IAAI,GAAG,KAAK,EAAE;YAChB,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE;YACzB,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE;YAC7B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,4BAAqB,IAAI,CAAE,CAAC,CAAC;SAC9C;QACD,IAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEO,6BAAW,GAAnB,UAAoB,MAAsB,EAAE,KAAa;;QACvD,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,EAAE,EAAE;YACb,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;SAC3B;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE;YACzB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE;YAC7B,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,2BAAoB,IAAI,CAAE,CAAC,CAAC;SAC7C;;YACD,KAAmB,oCAAM,iFAAE;gBAAtB,IAAM,IAAI;gBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;aAChC;;;;;;;;;IACH,CAAC;IAEO,uCAAqB,GAA7B,UAA8B,MAA+B,EAAE,IAA2B;;QACxF,IAAI,KAAK,GAAG,CAAC,CAAC;;YAEd,KAAkB,gCAAI,uEAAE;gBAAnB,IAAM,GAAG;gBACZ,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;oBAC7B,KAAK,EAAE,CAAC;iBACT;aACF;;;;;;;;;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,2BAAS,GAAjB,UAAkB,MAA+B,EAAE,KAAa;;QAC9D,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAE3F,IAAI,IAAI,GAAG,EAAE,EAAE;YACb,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;SAC3B;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE;YACzB,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE;YAC7B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,gCAAyB,IAAI,CAAE,CAAC,CAAC;SAClD;;YAED,KAAkB,gCAAI,uEAAE;gBAAnB,IAAM,GAAG;gBACZ,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAE1B,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,KAAK,KAAK,SAAS,CAAC,EAAE;oBAClD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oBACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;iBACjC;aACF;;;;;;;;;IACH,CAAC;IAEO,iCAAe,GAAvB,UAAwB,GAAY;QAClC,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAI,IAAI,KAAK,CAAC,EAAE;YACd,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACrB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACrB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACrB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,KAAK,EAAE,EAAE;YACtB,YAAY;YACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,GAAG,KAAK,EAAE;YACvB,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE;YACzB,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE;YAC7B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,sCAA+B,IAAI,CAAE,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,yBAAO,GAAf,UAAgB,KAAa;QAC3B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,MAAyB;QACxC,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;IACnB,CAAC;IAEO,yBAAO,GAAf,UAAgB,KAAa;QAC3B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,0BAAQ,GAAhB,UAAiB,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IACH,cAAC;AAAD,CAAC;;;;AC3ZmC;AAyDpC,IAAM,oBAAoB,GAAkB,EAAE,CAAC;AAE/C;;;;;GAKG;AACI,SAAS,MAAM,CACpB,KAAc,EACd,OAAiF;IAAjF,oCAAsD,oBAA2B;IAEjF,IAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,cAAc,EACrB,OAA6C,CAAC,OAAO,EACtD,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,iBAAiB,EACzB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,mBAAmB,CAC5B,CAAC;IACF,OAAO,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC;;;AChFM,SAAS,UAAU,CAAC,IAAY;IACrC,OAAO,UAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,eAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAE,CAAC;AACnF,CAAC;;;;;;;;;;;;;;ACF2C;AAE5C,IAAM,sBAAsB,GAAG,EAAE,CAAC;AAClC,IAAM,0BAA0B,GAAG,EAAE,CAAC;AAWtC;IAKE,0BAAqB,YAAqC,EAAW,eAA4C;QAA5F,oEAAqC;QAAW,8EAA4C;QAA5F,iBAAY,GAAZ,YAAY,CAAyB;QAAW,oBAAe,GAAf,eAAe,CAA6B;QAJjH,QAAG,GAAG,CAAC,CAAC;QACR,SAAI,GAAG,CAAC,CAAC;QAIP,oDAAoD;QACpD,sEAAsE;QACtE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACtB;IACH,CAAC;IAEM,sCAAW,GAAlB,UAAmB,UAAkB;QACnC,OAAO,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC;IAC3D,CAAC;IAEO,+BAAI,GAAZ,UAAa,KAAiB,EAAE,WAAmB,EAAE,UAAkB;;QACrE,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAE,CAAC;;YAE7C,UAAU,EAAE,KAAqB,+CAAO,sFAAE;gBAAzB,IAAM,MAAM;gBAC3B,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;gBAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;oBACnC,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;wBAC7C,SAAS,UAAU,CAAC;qBACrB;iBACF;gBACD,OAAO,MAAM,CAAC,GAAG,CAAC;aACnB;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gCAAK,GAAb,UAAc,KAAiB,EAAE,KAAa;QAC5C,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;QAC/C,IAAM,MAAM,GAAmB,EAAE,KAAK,SAAE,GAAG,EAAE,KAAK,EAAE,CAAC;QAErD,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE;YAC1C,sBAAsB;YACtB,yCAAyC;YACzC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;SACxD;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtB;IACH,CAAC;IAEM,iCAAM,GAAb,UAAc,KAAiB,EAAE,WAAmB,EAAE,UAAkB;QACtE,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAC9D,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,WAAW,CAAC;SACpB;QACD,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAM,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QACzD,4IAA4I;QAC5I,IAAM,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,GAAG,UAAU,CAAC,CAAC;QACxG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACnC,OAAO,GAAG,CAAC;IACb,CAAC;IACH,uBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3E+C;AACsB;AACR;AACoB;AACX;AACL;AACtB;AAU5C,IAAM,iBAAiB,GAAG,UAAC,GAAY;IACrC,IAAM,OAAO,GAAG,OAAO,GAAG,CAAC;IAE3B,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ,CAAC;AACtD,CAAC,CAAC;AAmBF,IAAM,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAE9B,IAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,IAAM,WAAW,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAEtD,8BAA8B;AAC9B,sEAAsE;AAC/D,IAAM,6BAA6B,GAAiB,CAAC;IAC1D,IAAI;QACF,kDAAkD;QAClD,yCAAyC;QACzC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;KACvB;IAAC,OAAO,CAAM,EAAE;QACf,OAAO,CAAC,CAAC,WAAW,CAAC;KACtB;IACD,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AACnC,CAAC,CAAC,EAAE,CAAC;AAEL,IAAM,SAAS,GAAG,IAAI,6BAA6B,CAAC,mBAAmB,CAAC,CAAC;AAEzE,IAAM,sBAAsB,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAEtD;IASE,iBACmB,cAAoF,EACpF,OAAuC,EACvC,YAAyB,EACzB,YAAyB,EACzB,cAA2B,EAC3B,YAAyB,EACzB,YAAyB,EACzB,UAAsD;QAPtD,kDAAkD,2BAAkC;QACpF,oCAAuB,SAAgB;QACvC,8CAAe,UAAU;QACzB,8CAAe,UAAU;QACzB,kDAAiB,UAAU;QAC3B,8CAAe,UAAU;QACzB,8CAAe,UAAU;QACzB,gEAAsD;QAPtD,mBAAc,GAAd,cAAc,CAAsE;QACpF,YAAO,GAAP,OAAO,CAAgC;QACvC,iBAAY,GAAZ,YAAY,CAAa;QACzB,iBAAY,GAAZ,YAAY,CAAa;QACzB,mBAAc,GAAd,cAAc,CAAa;QAC3B,iBAAY,GAAZ,YAAY,CAAa;QACzB,iBAAY,GAAZ,YAAY,CAAa;QACzB,eAAU,GAAV,UAAU,CAA4C;QAhBjE,aAAQ,GAAG,CAAC,CAAC;QACb,QAAG,GAAG,CAAC,CAAC;QAER,SAAI,GAAG,UAAU,CAAC;QAClB,UAAK,GAAG,WAAW,CAAC;QACpB,aAAQ,GAAG,kBAAkB,CAAC;QACrB,UAAK,GAAsB,EAAE,CAAC;IAW5C,CAAC;IAEI,mCAAiB,GAAzB;QACE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtB,6DAA6D;IAC/D,CAAC;IAEO,2BAAS,GAAjB,UAAkB,MAAwC;QACxD,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAEO,8BAAY,GAApB,UAAqB,MAAwC;QAC3D,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACjE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SACxB;aAAM;YACL,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,IAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEzC,iCAAiC;YACjC,IAAM,SAAS,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YACxE,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC7B,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SAC3B;IACH,CAAC;IAEO,8BAAY,GAApB,UAAqB,IAAY;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;IACjD,CAAC;IAEO,sCAAoB,GAA5B,UAA6B,SAAiB;QACtC,SAAgB,IAAI,EAAlB,IAAI,YAAE,GAAG,SAAS,CAAC;QAC3B,OAAO,IAAI,UAAU,CAAC,gBAAS,IAAI,CAAC,UAAU,GAAG,GAAG,iBAAO,IAAI,CAAC,UAAU,sCAA4B,SAAS,MAAG,CAAC,CAAC;IACtH,CAAC;IAED;;;OAGG;IACI,wBAAM,GAAb,UAAc,MAAwC;QACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEvB,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACxB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC3C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,6BAAW,GAAnB,UAAoB,MAAwC;;;;oBAC1D,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;;;yBAEhB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;oBACzB,qBAAM,IAAI,CAAC,YAAY,EAAE;;oBAAzB,SAAyB,CAAC;;;;;KAE7B;IAEY,6BAAW,GAAxB,UAAyB,MAAuD;;;;;;;;wBAC1E,OAAO,GAAG,KAAK,CAAC;;;;wBAEO,+BAAM;;;;;wBAAhB,MAAM;wBACrB,IAAI,OAAO,EAAE;4BACX,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;yBAChD;wBAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;wBAE1B,IAAI;4BACF,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;4BAC7B,OAAO,GAAG,IAAI,CAAC;yBAChB;wBAAC,OAAO,CAAC,EAAE;4BACV,IAAI,CAAC,CAAC,CAAC,YAAY,6BAA6B,CAAC,EAAE;gCACjD,MAAM,CAAC,CAAC,CAAC,UAAU;6BACpB;4BACD,cAAc;yBACf;wBACD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;wBAG5B,IAAI,OAAO,EAAE;4BACX,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;gCACxB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;6BAChD;4BACD,sBAAO,MAAM,EAAC;yBACf;wBAEK,KAA8B,IAAI,EAAhC,QAAQ,gBAAE,GAAG,WAAE,QAAQ,eAAU;wBACzC,MAAM,IAAI,UAAU,CAClB,uCAAgC,UAAU,CAAC,QAAQ,CAAC,iBAAO,QAAQ,eAAK,GAAG,4BAAyB,CACrG,CAAC;;;;KACH;IAEM,mCAAiB,GAAxB,UACE,MAAuD;QAEvD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,8BAAY,GAAnB,UAAoB,MAAuD;QACzE,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEc,kCAAgB,GAA/B,UAAgC,MAAuD,EAAE,OAAgB;;;;;;;wBACnG,qBAAqB,GAAG,OAAO,CAAC;wBAChC,cAAc,GAAG,CAAC,CAAC,CAAC;;;;wBAEG,+BAAM;;;;;wBAAhB,MAAM;wBACrB,IAAI,OAAO,IAAI,cAAc,KAAK,CAAC,EAAE;4BACnC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;yBAChD;wBAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;wBAE1B,IAAI,qBAAqB,EAAE;4BACzB,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;4BACtC,qBAAqB,GAAG,KAAK,CAAC;4BAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC;yBACjB;;;;;;iCAGY,EAAE;qDACL,IAAI,CAAC,YAAY,EAAE;4BAAzB,gCAAyB;;wBAAzB,SAAyB,CAAC;wBAC1B,IAAI,EAAE,cAAc,KAAK,CAAC,EAAE;4BAC1B,wBAAM;yBACP;;;;;wBAGH,IAAI,CAAC,CAAC,GAAC,YAAY,6BAA6B,CAAC,EAAE;4BACjD,MAAM,GAAC,CAAC,CAAC,UAAU;yBACpB;;;wBAGH,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;KAE7B;IAEO,8BAAY,GAApB;QACE,MAAM,EAAE,OAAO,IAAI,EAAE;YACnB,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACrC,IAAI,MAAM,SAAS,CAAC;YAEpB,IAAI,QAAQ,IAAI,IAAI,EAAE;gBACpB,0CAA0C;gBAC1C,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;aAC3B;iBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE;gBAC1B,IAAI,QAAQ,GAAG,IAAI,EAAE;oBACnB,0CAA0C;oBAC1C,MAAM,GAAG,QAAQ,CAAC;iBACnB;qBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE;oBAC1B,iCAAiC;oBACjC,IAAM,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAC7B,IAAI,IAAI,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChB,SAAS,MAAM,CAAC;qBACjB;yBAAM;wBACL,MAAM,GAAG,EAAE,CAAC;qBACb;iBACF;qBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE;oBAC1B,mCAAmC;oBACnC,IAAM,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAC7B,IAAI,IAAI,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChB,SAAS,MAAM,CAAC;qBACjB;yBAAM;wBACL,MAAM,GAAG,EAAE,CAAC;qBACb;iBACF;qBAAM;oBACL,iCAAiC;oBACjC,IAAM,UAAU,GAAG,QAAQ,GAAG,IAAI,CAAC;oBACnC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;iBAC/C;aACF;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,MAAM;gBACN,MAAM,GAAG,IAAI,CAAC;aACf;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,QAAQ;gBACR,MAAM,GAAG,KAAK,CAAC;aAChB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,OAAO;gBACP,MAAM,GAAG,IAAI,CAAC;aACf;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;aACxB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,UAAU;gBACV,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,UAAU;gBACV,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,UAAU;gBACV,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,QAAQ;gBACR,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;aACxB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;aACzB;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,QAAQ;gBACR,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;aAC/C;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;aAC/C;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;aAC/C;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;iBACjB;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAC;iBACb;aACF;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;iBACjB;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAC;iBACb;aACF;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;iBACjB;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAC;iBACb;aACF;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;iBACjB;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAC;iBACb;aACF;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,QAAQ;gBACR,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,YAAY;gBACZ,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aACtC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,QAAQ;gBACR,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACxC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACxC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B,SAAS;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACxC;iBAAM;gBACL,MAAM,IAAI,WAAW,CAAC,kCAA2B,UAAU,CAAC,QAAQ,CAAC,CAAE,CAAC,CAAC;aAC1E;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEhB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,kBAAkB;gBAClB,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;gBACvC,IAAI,KAAK,CAAC,IAAI,wBAAgB,EAAE;oBAC9B,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;oBACrC,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjB,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,EAAE;wBACjC,KAAK,CAAC,GAAG,EAAE,CAAC;wBACZ,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;qBACtB;yBAAM;wBACL,SAAS,MAAM,CAAC;qBACjB;iBACF;qBAAM,IAAI,KAAK,CAAC,IAAI,0BAAkB,EAAE;oBACvC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;wBAC9B,MAAM,IAAI,WAAW,CAAC,+CAA+C,GAAG,OAAO,MAAM,CAAC,CAAC;qBACxF;oBACD,IAAI,MAAM,KAAK,WAAW,EAAE;wBAC1B,MAAM,IAAI,WAAW,CAAC,kCAAkC,CAAC,CAAC;qBAC3D;oBAED,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC;oBACnB,KAAK,CAAC,IAAI,0BAAkB,CAAC;oBAC7B,SAAS,MAAM,CAAC;iBACjB;qBAAM;oBACL,mDAAmD;oBAEnD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,MAAM,CAAC;oBAC/B,KAAK,CAAC,SAAS,EAAE,CAAC;oBAElB,IAAI,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,EAAE;wBAClC,KAAK,CAAC,GAAG,EAAE,CAAC;wBACZ,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;qBACpB;yBAAM;wBACL,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;wBACjB,KAAK,CAAC,IAAI,wBAAgB,CAAC;wBAC3B,SAAS,MAAM,CAAC;qBACjB;iBACF;aACF;YAED,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAEO,8BAAY,GAApB;QACE,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,EAAE;YACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,sDAAsD;SACvD;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEO,0BAAQ,GAAhB;QACE,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC;IACrC,CAAC;IAEO,+BAAa,GAArB;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAErC,QAAQ,QAAQ,EAAE;YAChB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC;gBACP,IAAI,QAAQ,GAAG,IAAI,EAAE;oBACnB,OAAO,QAAQ,GAAG,IAAI,CAAC;iBACxB;qBAAM;oBACL,MAAM,IAAI,WAAW,CAAC,wCAAiC,UAAU,CAAC,QAAQ,CAAC,CAAE,CAAC,CAAC;iBAChF;aACF;SACF;IACH,CAAC;IAEO,8BAAY,GAApB,UAAqB,IAAY;QAC/B,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE;YAC5B,MAAM,IAAI,WAAW,CAAC,2CAAoC,IAAI,qCAA2B,IAAI,CAAC,YAAY,MAAG,CAAC,CAAC;SAChH;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACd,IAAI,uBAAe;YACnB,IAAI;YACJ,GAAG,EAAE,IAAI;YACT,SAAS,EAAE,CAAC;YACZ,GAAG,EAAE,EAAE;SACR,CAAC,CAAC;IACL,CAAC;IAEO,gCAAc,GAAtB,UAAuB,IAAY;QACjC,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE;YAC9B,MAAM,IAAI,WAAW,CAAC,6CAAsC,IAAI,iCAAuB,IAAI,CAAC,cAAc,MAAG,CAAC,CAAC;SAChH;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACd,IAAI,qBAAa;YACjB,IAAI;YACJ,KAAK,EAAE,IAAI,KAAK,CAAU,IAAI,CAAC;YAC/B,QAAQ,EAAE,CAAC;SACZ,CAAC,CAAC;IACL,CAAC;IAEO,kCAAgB,GAAxB,UAAyB,UAAkB,EAAE,YAAoB;;QAC/D,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE;YAClC,MAAM,IAAI,WAAW,CACnB,kDAA2C,UAAU,+BAAqB,IAAI,CAAC,YAAY,MAAG,CAC/F,CAAC;SACH;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG,YAAY,GAAG,UAAU,EAAE;YAChE,MAAM,SAAS,CAAC;SACjB;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC;QACvC,IAAI,MAAc,CAAC;QACnB,IAAI,IAAI,CAAC,aAAa,EAAE,KAAI,UAAI,CAAC,UAAU,0CAAE,WAAW,CAAC,UAAU,CAAC,GAAE;YACpE,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SACjE;aAAM,IAAI,UAAU,GAAG,sBAAsB,EAAE;YAC9C,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SACvD;aAAM;YACL,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SACvD;QACD,IAAI,CAAC,GAAG,IAAI,YAAY,GAAG,UAAU,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,+BAAa,GAArB;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;YACjD,OAAO,KAAK,CAAC,IAAI,0BAAkB,CAAC;SACrC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,8BAAY,GAApB,UAAqB,UAAkB,EAAE,UAAkB;QACzD,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE;YAClC,MAAM,IAAI,WAAW,CAAC,2CAAoC,UAAU,+BAAqB,IAAI,CAAC,YAAY,MAAG,CAAC,CAAC;SAChH;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,UAAU,CAAC,EAAE;YAC/C,MAAM,SAAS,CAAC;SACjB;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;QACrC,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,GAAG,IAAI,UAAU,GAAG,UAAU,CAAC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iCAAe,GAAvB,UAAwB,IAAY,EAAE,UAAkB;QACtD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE;YAC5B,MAAM,IAAI,WAAW,CAAC,2CAAoC,IAAI,+BAAqB,IAAI,CAAC,YAAY,MAAG,CAAC,CAAC;SAC1G;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;QACzD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACjE,CAAC;IAEO,wBAAM,GAAd;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAEO,yBAAO,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEO,yBAAO,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEO,wBAAM,GAAd;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,wBAAM,GAAd;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAO,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IACH,cAAC;AAAD,CAAC;;;;AClnBmC;AA0C7B,IAAM,oBAAoB,GAAkB,EAAE,CAAC;AAEtD;;;;;;;;GAQG;AACI,SAAS,MAAM,CACpB,MAAwC,EACxC,OAAiF;IAAjF,oCAAsD,oBAA2B;IAEjF,IAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,cAAc,EACrB,OAA6C,CAAC,OAAO,EACtD,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;IACF,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;GAMG;AACI,SAAS,WAAW,CACzB,MAAwC,EACxC,OAAiF;IAAjF,oCAAsD,oBAA2B;IAEjF,IAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,cAAc,EACrB,OAA6C,CAAC,OAAO,EACtD,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;IACF,OAAO,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;;;AC1FD,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB,SAAS,eAAe,CAAI,MAA6B;IAC9D,OAAQ,MAAc,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;AACvD,CAAC;AAED,SAAS,aAAa,CAAI,KAA2B;IACnD,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;KAC5E;AACH,CAAC;AAEM,SAAgB,uBAAuB,CAAI,MAAyB;;;;;;oBACnE,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;;;;;;6BAGrB,EAAE;oBACa,kCAAM,MAAM,CAAC,IAAI,EAAE;;oBAArC,KAAkB,SAAmB,EAAnC,IAAI,YAAE,KAAK;yBACf,IAAI,EAAJ,wBAAI;;wBACN,iCAAO;;oBAET,aAAa,CAAC,KAAK,CAAC,CAAC;sDACf,KAAK;wBAAX,gCAAW;;oBAAX,SAAW,CAAC;;;;oBAGd,MAAM,CAAC,WAAW,EAAE,CAAC;;;;;;CAExB;AAEM,SAAS,mBAAmB,CAAI,UAAiC;IACtE,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE;QAC/B,OAAO,UAAU,CAAC;KACnB;SAAM;QACL,OAAO,uBAAuB,CAAC,UAAU,CAAC,CAAC;KAC5C;AACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCmC;AACiB;AACL;AAKhD;;;GAGG;AACK,SAAe,WAAW,CAChC,UAAgE,EAChE,OAAiF;IAAjF,oCAAsD,oBAA2B;;;;YAE3E,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAEzC,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,cAAc,EACrB,OAA6C,CAAC,OAAO,EACtD,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;YACF,sBAAO,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,EAAC;;;CACpC;AAED;;;GAGG;AACK,SAAS,iBAAiB,CAChC,UAAgE,EAChE,OAAiF;IAAjF,oCAAsD,oBAA2B;IAEjF,IAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAE/C,IAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,cAAc,EACrB,OAA6C,CAAC,OAAO,EACtD,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;IAEF,OAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED;;;GAGG;AACI,SAAS,iBAAiB,CAC/B,UAAgE,EAChE,OAAiF;IAAjF,oCAAsD,oBAA2B;IAEjF,IAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAE/C,IAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,cAAc,EACrB,OAA6C,CAAC,OAAO,EACtD,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;IAEF,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACI,SAAS,YAAY,CAC1B,UAAgE,EAChE,OAAiF;IAAjF,oCAAsD,oBAA2B;IAEjF,OAAO,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;;;ACnFD,kBAAkB;AAEgB;AAChB;AAI6B;AAChB;AAIiE;AACrB;AAER;AACvB;AACmB;AAE3B;AACjB;AAEnB,kCAAkC;AAEgB;AACxB;AAGU;AACjB;AASE;AAQnB", "sources": ["webpack://MessagePack/webpack/universalModuleDefinition", "webpack://MessagePack/webpack/bootstrap", "webpack://MessagePack/webpack/runtime/define property getters", "webpack://MessagePack/webpack/runtime/hasOwnProperty shorthand", "webpack://MessagePack/webpack/runtime/make namespace object", "webpack://MessagePack/./src/utils/int.ts", "webpack://MessagePack/./src/utils/utf8.ts", "webpack://MessagePack/./src/ExtData.ts", "webpack://MessagePack/./src/DecodeError.ts", "webpack://MessagePack/./src/timestamp.ts", "webpack://MessagePack/./src/ExtensionCodec.ts", "webpack://MessagePack/./src/utils/typedArrays.ts", "webpack://MessagePack/./src/Encoder.ts", "webpack://MessagePack/./src/encode.ts", "webpack://MessagePack/./src/utils/prettyByte.ts", "webpack://MessagePack/./src/CachedKeyDecoder.ts", "webpack://MessagePack/./src/Decoder.ts", "webpack://MessagePack/./src/decode.ts", "webpack://MessagePack/./src/utils/stream.ts", "webpack://MessagePack/./src/decodeAsync.ts", "webpack://MessagePack/./src/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"MessagePack\"] = factory();\n\telse\n\t\troot[\"MessagePack\"] = factory();\n})(this, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Integer Utility\n\nexport const UINT32_MAX = 0xffff_ffff;\n\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\n\nexport function setUint64(view: DataView, offset: number, value: number): void {\n  const high = value / 0x1_0000_0000;\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function setInt64(view: DataView, offset: number, value: number): void {\n  const high = Math.floor(value / 0x1_0000_0000);\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function getInt64(view: DataView, offset: number): number {\n  const high = view.getInt32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n\nexport function getUint64(view: <PERSON>View, offset: number): number {\n  const high = view.getUint32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n", "/* eslint-disable @typescript-eslint/no-unnecessary-condition */\nimport { UINT32_MAX } from \"./int\";\n\nconst TEXT_ENCODING_AVAILABLE =\n  (typeof process === \"undefined\" || process?.env?.[\"TEXT_ENCODING\"] !== \"never\") &&\n  typeof TextEncoder !== \"undefined\" &&\n  typeof TextDecoder !== \"undefined\";\n\nexport function utf8Count(str: string): number {\n  const strLength = str.length;\n\n  let byteLength = 0;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      byteLength++;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      byteLength += 2;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        byteLength += 3;\n      } else {\n        // 4-byte\n        byteLength += 4;\n      }\n    }\n  }\n  return byteLength;\n}\n\nexport function utf8EncodeJs(str: string, output: Uint8Array, outputOffset: number): void {\n  const strLength = str.length;\n  let offset = outputOffset;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      output[offset++] = value;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      } else {\n        // 4-byte\n        output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n        output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      }\n    }\n\n    output[offset++] = (value & 0x3f) | 0x80;\n  }\n}\n\nconst sharedTextEncoder = TEXT_ENCODING_AVAILABLE ? new TextEncoder() : undefined;\nexport const TEXT_ENCODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n  ? UINT32_MAX\n  : typeof process !== \"undefined\" && process?.env?.[\"TEXT_ENCODING\"] !== \"force\"\n  ? 200\n  : 0;\n\nfunction utf8EncodeTEencode(str: string, output: Uint8Array, outputOffset: number): void {\n  output.set(sharedTextEncoder!.encode(str), outputOffset);\n}\n\nfunction utf8EncodeTEencodeInto(str: string, output: Uint8Array, outputOffset: number): void {\n  sharedTextEncoder!.encodeInto(str, output.subarray(outputOffset));\n}\n\nexport const utf8EncodeTE = sharedTextEncoder?.encodeInto ? utf8EncodeTEencodeInto : utf8EncodeTEencode;\n\nconst CHUNK_SIZE = 0x1_000;\n\nexport function utf8DecodeJs(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  let offset = inputOffset;\n  const end = offset + byteLength;\n\n  const units: Array<number> = [];\n  let result = \"\";\n  while (offset < end) {\n    const byte1 = bytes[offset++]!;\n    if ((byte1 & 0x80) === 0) {\n      // 1 byte\n      units.push(byte1);\n    } else if ((byte1 & 0xe0) === 0xc0) {\n      // 2 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 6) | byte2);\n    } else if ((byte1 & 0xf0) === 0xe0) {\n      // 3 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n    } else if ((byte1 & 0xf8) === 0xf0) {\n      // 4 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      const byte4 = bytes[offset++]! & 0x3f;\n      let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n      if (unit > 0xffff) {\n        unit -= 0x10000;\n        units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n        unit = 0xdc00 | (unit & 0x3ff);\n      }\n      units.push(unit);\n    } else {\n      units.push(byte1);\n    }\n\n    if (units.length >= CHUNK_SIZE) {\n      result += String.fromCharCode(...units);\n      units.length = 0;\n    }\n  }\n\n  if (units.length > 0) {\n    result += String.fromCharCode(...units);\n  }\n\n  return result;\n}\n\nconst sharedTextDecoder = TEXT_ENCODING_AVAILABLE ? new TextDecoder() : null;\nexport const TEXT_DECODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n  ? UINT32_MAX\n  : typeof process !== \"undefined\" && process?.env?.[\"TEXT_DECODER\"] !== \"force\"\n  ? 200\n  : 0;\n\nexport function utf8DecodeTD(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n  return sharedTextDecoder!.decode(stringBytes);\n}\n", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nexport class ExtData {\n  constructor(readonly type: number, readonly data: Uint8Array) {}\n}\n", "export class DecodeError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // fix the prototype chain in a cross-platform way\n    const proto: typeof DecodeError.prototype = Object.create(DecodeError.prototype);\n    Object.setPrototypeOf(this, proto);\n\n    Object.defineProperty(this, \"name\", {\n      configurable: true,\n      enumerable: false,\n      value: DecodeError.name,\n    });\n  }\n}\n", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError\";\nimport { getInt64, setInt64 } from \"./utils/int\";\n\nexport const EXT_TIMESTAMP = -1;\n\nexport type TimeSpec = {\n  sec: number;\n  nsec: number;\n};\n\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\n\nexport function encodeTimeSpecToTimestamp({ sec, nsec }: TimeSpec): Uint8Array {\n  if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n    // Here sec >= 0 && nsec >= 0\n    if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n      // timestamp 32 = { sec32 (unsigned) }\n      const rv = new Uint8Array(4);\n      const view = new DataView(rv.buffer);\n      view.setUint32(0, sec);\n      return rv;\n    } else {\n      // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n      const secHigh = sec / 0x100000000;\n      const secLow = sec & 0xffffffff;\n      const rv = new Uint8Array(8);\n      const view = new DataView(rv.buffer);\n      // nsec30 | secHigh2\n      view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n      // secLow32\n      view.setUint32(4, secLow);\n      return rv;\n    }\n  } else {\n    // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n    const rv = new Uint8Array(12);\n    const view = new DataView(rv.buffer);\n    view.setUint32(0, nsec);\n    setInt64(view, 4, sec);\n    return rv;\n  }\n}\n\nexport function encodeDateToTimeSpec(date: Date): TimeSpec {\n  const msec = date.getTime();\n  const sec = Math.floor(msec / 1e3);\n  const nsec = (msec - sec * 1e3) * 1e6;\n\n  // Normalizes { sec, nsec } to ensure nsec is unsigned.\n  const nsecInSec = Math.floor(nsec / 1e9);\n  return {\n    sec: sec + nsecInSec,\n    nsec: nsec - nsecInSec * 1e9,\n  };\n}\n\nexport function encodeTimestampExtension(object: unknown): Uint8Array | null {\n  if (object instanceof Date) {\n    const timeSpec = encodeDateToTimeSpec(object);\n    return encodeTimeSpecToTimestamp(timeSpec);\n  } else {\n    return null;\n  }\n}\n\nexport function decodeTimestampToTimeSpec(data: Uint8Array): TimeSpec {\n  const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n\n  // data may be 32, 64, or 96 bits\n  switch (data.byteLength) {\n    case 4: {\n      // timestamp 32 = { sec32 }\n      const sec = view.getUint32(0);\n      const nsec = 0;\n      return { sec, nsec };\n    }\n    case 8: {\n      // timestamp 64 = { nsec30, sec34 }\n      const nsec30AndSecHigh2 = view.getUint32(0);\n      const secLow32 = view.getUint32(4);\n      const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n      const nsec = nsec30AndSecHigh2 >>> 2;\n      return { sec, nsec };\n    }\n    case 12: {\n      // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n\n      const sec = getInt64(view, 4);\n      const nsec = view.getUint32(0);\n      return { sec, nsec };\n    }\n    default:\n      throw new DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n  }\n}\n\nexport function decodeTimestampExtension(data: Uint8Array): Date {\n  const timeSpec = decodeTimestampToTimeSpec(data);\n  return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\n\nexport const timestampExtension = {\n  type: EXT_TIMESTAMP,\n  encode: encodeTimestampExtension,\n  decode: decodeTimestampExtension,\n};\n", "// ExtensionCodec to handle MessagePack extensions\n\nimport { ExtData } from \"./ExtData\";\nimport { timestampExtension } from \"./timestamp\";\n\nexport type ExtensionDecoderType<ContextType> = (\n  data: Uint8Array,\n  extensionType: number,\n  context: ContextType,\n) => unknown;\n\nexport type ExtensionEncoderType<ContextType> = (input: unknown, context: ContextType) => Uint8Array | null;\n\n// immutable interface to ExtensionCodec\nexport type ExtensionCodecType<ContextType> = {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n  tryToEncode(object: unknown, context: ContextType): ExtData | null;\n  decode(data: Uint8Array, extType: number, context: ContextType): unknown;\n};\n\nexport class ExtensionCodec<ContextType = undefined> implements ExtensionCodecType<ContextType> {\n  public static readonly defaultCodec: ExtensionCodecType<undefined> = new ExtensionCodec();\n\n  // ensures ExtensionCodecType<X> matches ExtensionCodec<X>\n  // this will make type errors a lot more clear\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n\n  // built-in extensions\n  private readonly builtInEncoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly builtInDecoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  // custom extensions\n  private readonly encoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly decoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  public constructor() {\n    this.register(timestampExtension);\n  }\n\n  public register({\n    type,\n    encode,\n    decode,\n  }: {\n    type: number;\n    encode: ExtensionEncoderType<ContextType>;\n    decode: ExtensionDecoderType<ContextType>;\n  }): void {\n    if (type >= 0) {\n      // custom extensions\n      this.encoders[type] = encode;\n      this.decoders[type] = decode;\n    } else {\n      // built-in extensions\n      const index = 1 + type;\n      this.builtInEncoders[index] = encode;\n      this.builtInDecoders[index] = decode;\n    }\n  }\n\n  public tryToEncode(object: unknown, context: ContextType): ExtData | null {\n    // built-in extensions\n    for (let i = 0; i < this.builtInEncoders.length; i++) {\n      const encodeExt = this.builtInEncoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = -1 - i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    // custom extensions\n    for (let i = 0; i < this.encoders.length; i++) {\n      const encodeExt = this.encoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    if (object instanceof ExtData) {\n      // to keep ExtData as is\n      return object;\n    }\n    return null;\n  }\n\n  public decode(data: Uint8Array, type: number, context: ContextType): unknown {\n    const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n    if (decodeExt) {\n      return decodeExt(data, type, context);\n    } else {\n      // decode() does not fail, returns ExtData instead.\n      return new ExtData(type, data);\n    }\n  }\n}\n", "export function ensureUint8Array(buffer: ArrayLike<number> | Uint8Array | ArrayBufferView | ArrayBuffer): Uint8Array {\n  if (buffer instanceof Uint8Array) {\n    return buffer;\n  } else if (ArrayBuffer.isView(buffer)) {\n    return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  } else if (buffer instanceof ArrayBuffer) {\n    return new Uint8Array(buffer);\n  } else {\n    // ArrayLike<number>\n    return Uint8Array.from(buffer);\n  }\n}\n\nexport function createDataView(buffer: ArrayLike<number> | ArrayBufferView | ArrayBuffer): DataView {\n  if (buffer instanceof ArrayBuffer) {\n    return new DataView(buffer);\n  }\n\n  const bufferView = ensureUint8Array(buffer);\n  return new DataView(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\n}\n", "import { utf8EncodeJs, utf8Count, TEXT_ENCODER_THRESHOLD, utf8EncodeTE } from \"./utils/utf8\";\nimport { ExtensionCodec, ExtensionCodecType } from \"./ExtensionCodec\";\nimport { setInt64, setUint64 } from \"./utils/int\";\nimport { ensureUint8Array } from \"./utils/typedArrays\";\nimport type { ExtData } from \"./ExtData\";\n\nexport const DEFAULT_MAX_DEPTH = 100;\nexport const DEFAULT_INITIAL_BUFFER_SIZE = 2048;\n\nexport class Encoder<ContextType = undefined> {\n  private pos = 0;\n  private view = new DataView(new ArrayBuffer(this.initialBufferSize));\n  private bytes = new Uint8Array(this.view.buffer);\n\n  public constructor(\n    private readonly extensionCodec: ExtensionCodecType<ContextType> = ExtensionCodec.defaultCodec as any,\n    private readonly context: ContextType = undefined as any,\n    private readonly maxDepth = DEFAULT_MAX_DEPTH,\n    private readonly initialBufferSize = DEFAULT_INITIAL_BUFFER_SIZE,\n    private readonly sortKeys = false,\n    private readonly forceFloat32 = false,\n    private readonly ignoreUndefined = false,\n    private readonly forceIntegerToFloat = false,\n  ) {}\n\n  private reinitializeState() {\n    this.pos = 0;\n  }\n\n  /**\n   * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n   *\n   * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n   */\n  public encodeSharedRef(object: unknown): Uint8Array {\n    this.reinitializeState();\n    this.doEncode(object, 1);\n    return this.bytes.subarray(0, this.pos);\n  }\n\n  /**\n   * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n   */\n  public encode(object: unknown): Uint8Array {\n    this.reinitializeState();\n    this.doEncode(object, 1);\n    return this.bytes.slice(0, this.pos);\n  }\n\n  private doEncode(object: unknown, depth: number): void {\n    if (depth > this.maxDepth) {\n      throw new Error(`Too deep objects in depth ${depth}`);\n    }\n\n    if (object == null) {\n      this.encodeNil();\n    } else if (typeof object === \"boolean\") {\n      this.encodeBoolean(object);\n    } else if (typeof object === \"number\") {\n      this.encodeNumber(object);\n    } else if (typeof object === \"string\") {\n      this.encodeString(object);\n    } else {\n      this.encodeObject(object, depth);\n    }\n  }\n\n  private ensureBufferSizeToWrite(sizeToWrite: number) {\n    const requiredSize = this.pos + sizeToWrite;\n\n    if (this.view.byteLength < requiredSize) {\n      this.resizeBuffer(requiredSize * 2);\n    }\n  }\n\n  private resizeBuffer(newSize: number) {\n    const newBuffer = new ArrayBuffer(newSize);\n    const newBytes = new Uint8Array(newBuffer);\n    const newView = new DataView(newBuffer);\n\n    newBytes.set(this.bytes);\n\n    this.view = newView;\n    this.bytes = newBytes;\n  }\n\n  private encodeNil() {\n    this.writeU8(0xc0);\n  }\n\n  private encodeBoolean(object: boolean) {\n    if (object === false) {\n      this.writeU8(0xc2);\n    } else {\n      this.writeU8(0xc3);\n    }\n  }\n  private encodeNumber(object: number) {\n    if (Number.isSafeInteger(object) && !this.forceIntegerToFloat) {\n      if (object >= 0) {\n        if (object < 0x80) {\n          // positive fixint\n          this.writeU8(object);\n        } else if (object < 0x100) {\n          // uint 8\n          this.writeU8(0xcc);\n          this.writeU8(object);\n        } else if (object < 0x10000) {\n          // uint 16\n          this.writeU8(0xcd);\n          this.writeU16(object);\n        } else if (object < 0x100000000) {\n          // uint 32\n          this.writeU8(0xce);\n          this.writeU32(object);\n        } else {\n          // uint 64\n          this.writeU8(0xcf);\n          this.writeU64(object);\n        }\n      } else {\n        if (object >= -0x20) {\n          // negative fixint\n          this.writeU8(0xe0 | (object + 0x20));\n        } else if (object >= -0x80) {\n          // int 8\n          this.writeU8(0xd0);\n          this.writeI8(object);\n        } else if (object >= -0x8000) {\n          // int 16\n          this.writeU8(0xd1);\n          this.writeI16(object);\n        } else if (object >= -0x80000000) {\n          // int 32\n          this.writeU8(0xd2);\n          this.writeI32(object);\n        } else {\n          // int 64\n          this.writeU8(0xd3);\n          this.writeI64(object);\n        }\n      }\n    } else {\n      // non-integer numbers\n      if (this.forceFloat32) {\n        // float 32\n        this.writeU8(0xca);\n        this.writeF32(object);\n      } else {\n        // float 64\n        this.writeU8(0xcb);\n        this.writeF64(object);\n      }\n    }\n  }\n\n  private writeStringHeader(byteLength: number) {\n    if (byteLength < 32) {\n      // fixstr\n      this.writeU8(0xa0 + byteLength);\n    } else if (byteLength < 0x100) {\n      // str 8\n      this.writeU8(0xd9);\n      this.writeU8(byteLength);\n    } else if (byteLength < 0x10000) {\n      // str 16\n      this.writeU8(0xda);\n      this.writeU16(byteLength);\n    } else if (byteLength < 0x100000000) {\n      // str 32\n      this.writeU8(0xdb);\n      this.writeU32(byteLength);\n    } else {\n      throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n    }\n  }\n\n  private encodeString(object: string) {\n    const maxHeaderSize = 1 + 4;\n    const strLength = object.length;\n\n    if (strLength > TEXT_ENCODER_THRESHOLD) {\n      const byteLength = utf8Count(object);\n      this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n      this.writeStringHeader(byteLength);\n      utf8EncodeTE(object, this.bytes, this.pos);\n      this.pos += byteLength;\n    } else {\n      const byteLength = utf8Count(object);\n      this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n      this.writeStringHeader(byteLength);\n      utf8EncodeJs(object, this.bytes, this.pos);\n      this.pos += byteLength;\n    }\n  }\n\n  private encodeObject(object: unknown, depth: number) {\n    // try to encode objects with custom codec first of non-primitives\n    const ext = this.extensionCodec.tryToEncode(object, this.context);\n    if (ext != null) {\n      this.encodeExtension(ext);\n    } else if (Array.isArray(object)) {\n      this.encodeArray(object, depth);\n    } else if (ArrayBuffer.isView(object)) {\n      this.encodeBinary(object);\n    } else if (typeof object === \"object\") {\n      this.encodeMap(object as Record<string, unknown>, depth);\n    } else {\n      // symbol, function and other special object come here unless extensionCodec handles them.\n      throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n    }\n  }\n\n  private encodeBinary(object: ArrayBufferView) {\n    const size = object.byteLength;\n    if (size < 0x100) {\n      // bin 8\n      this.writeU8(0xc4);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // bin 16\n      this.writeU8(0xc5);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // bin 32\n      this.writeU8(0xc6);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large binary: ${size}`);\n    }\n    const bytes = ensureUint8Array(object);\n    this.writeU8a(bytes);\n  }\n\n  private encodeArray(object: Array<unknown>, depth: number) {\n    const size = object.length;\n    if (size < 16) {\n      // fixarray\n      this.writeU8(0x90 + size);\n    } else if (size < 0x10000) {\n      // array 16\n      this.writeU8(0xdc);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // array 32\n      this.writeU8(0xdd);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large array: ${size}`);\n    }\n    for (const item of object) {\n      this.doEncode(item, depth + 1);\n    }\n  }\n\n  private countWithoutUndefined(object: Record<string, unknown>, keys: ReadonlyArray<string>): number {\n    let count = 0;\n\n    for (const key of keys) {\n      if (object[key] !== undefined) {\n        count++;\n      }\n    }\n\n    return count;\n  }\n\n  private encodeMap(object: Record<string, unknown>, depth: number) {\n    const keys = Object.keys(object);\n    if (this.sortKeys) {\n      keys.sort();\n    }\n\n    const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n\n    if (size < 16) {\n      // fixmap\n      this.writeU8(0x80 + size);\n    } else if (size < 0x10000) {\n      // map 16\n      this.writeU8(0xde);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // map 32\n      this.writeU8(0xdf);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large map object: ${size}`);\n    }\n\n    for (const key of keys) {\n      const value = object[key];\n\n      if (!(this.ignoreUndefined && value === undefined)) {\n        this.encodeString(key);\n        this.doEncode(value, depth + 1);\n      }\n    }\n  }\n\n  private encodeExtension(ext: ExtData) {\n    const size = ext.data.length;\n    if (size === 1) {\n      // fixext 1\n      this.writeU8(0xd4);\n    } else if (size === 2) {\n      // fixext 2\n      this.writeU8(0xd5);\n    } else if (size === 4) {\n      // fixext 4\n      this.writeU8(0xd6);\n    } else if (size === 8) {\n      // fixext 8\n      this.writeU8(0xd7);\n    } else if (size === 16) {\n      // fixext 16\n      this.writeU8(0xd8);\n    } else if (size < 0x100) {\n      // ext 8\n      this.writeU8(0xc7);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // ext 16\n      this.writeU8(0xc8);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // ext 32\n      this.writeU8(0xc9);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large extension object: ${size}`);\n    }\n    this.writeI8(ext.type);\n    this.writeU8a(ext.data);\n  }\n\n  private writeU8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setUint8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU8a(values: ArrayLike<number>) {\n    const size = values.length;\n    this.ensureBufferSizeToWrite(size);\n\n    this.bytes.set(values, this.pos);\n    this.pos += size;\n  }\n\n  private writeI8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setInt8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setUint16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeI16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setInt16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeU32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setUint32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeI32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setInt32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n    this.view.setFloat32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n    this.view.setFloat64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeU64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setUint64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeI64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setInt64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n}\n", "import { Encoder } from \"./Encoder\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec\";\nimport type { ContextOf, SplitUndefined } from \"./context\";\n\nexport type EncodeOptions<ContextType = undefined> = Partial<\n  Readonly<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * The maximum depth in nested objects and arrays.\n     *\n     * Defaults to 100.\n     */\n    maxDepth: number;\n\n    /**\n     * The initial size of the internal buffer.\n     *\n     * Defaults to 2048.\n     */\n    initialBufferSize: number;\n\n    /**\n     * If `true`, the keys of an object is sorted. In other words, the encoded\n     * binary is canonical and thus comparable to another encoded binary.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    sortKeys: boolean;\n    /**\n     * If `true`, non-integer numbers are encoded in float32, not in float64 (the default).\n     *\n     * Only use it if precisions don't matter.\n     *\n     * Defaults to `false`.\n     */\n    forceFloat32: boolean;\n\n    /**\n     * If `true`, an object property with `undefined` value are ignored.\n     * e.g. `{ foo: undefined }` will be encoded as `{}`, as `JSON.stringify()` does.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    ignoreUndefined: boolean;\n\n    /**\n     * If `true`, integer numbers are encoded as floating point numbers,\n     * with the `forceFloat32` option taken into account.\n     *\n     * Defaults to `false`.\n     */\n    forceIntegerToFloat: boolean;\n  }>\n> &\n  ContextOf<ContextType>;\n\nconst defaultEncodeOptions: EncodeOptions = {};\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nexport function encode<ContextType = undefined>(\n  value: unknown,\n  options: EncodeOptions<SplitUndefined<ContextType>> = defaultEncodeOptions as any,\n): Uint8Array {\n  const encoder = new Encoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxDepth,\n    options.initialBufferSize,\n    options.sortKeys,\n    options.forceFloat32,\n    options.ignoreUndefined,\n    options.forceIntegerToFloat,\n  );\n  return encoder.encodeSharedRef(value);\n}\n", "export function prettyByte(byte: number): string {\n  return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n", "import { utf8DecodeJs } from \"./utils/utf8\";\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\n\nexport interface KeyDecoder {\n  canBeCached(byteLength: number): boolean;\n  decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string;\n}\ninterface KeyCacheRecord {\n  readonly bytes: Uint8Array;\n  readonly str: string;\n}\n\nexport class CachedKeyDecoder implements KeyDecoder {\n  hit = 0;\n  miss = 0;\n  private readonly caches: Array<Array<KeyCacheRecord>>;\n\n  constructor(readonly maxKeyLength = DEFAULT_MAX_KEY_LENGTH, readonly maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n    // avoid `new Array(N)`, which makes a sparse array,\n    // because a sparse array is typically slower than a non-sparse array.\n    this.caches = [];\n    for (let i = 0; i < this.maxKeyLength; i++) {\n      this.caches.push([]);\n    }\n  }\n\n  public canBeCached(byteLength: number): boolean {\n    return byteLength > 0 && byteLength <= this.maxKeyLength;\n  }\n\n  private find(bytes: Uint8Array, inputOffset: number, byteLength: number): string | null {\n    const records = this.caches[byteLength - 1]!;\n\n    FIND_CHUNK: for (const record of records) {\n      const recordBytes = record.bytes;\n\n      for (let j = 0; j < byteLength; j++) {\n        if (recordBytes[j] !== bytes[inputOffset + j]) {\n          continue FIND_CHUNK;\n        }\n      }\n      return record.str;\n    }\n    return null;\n  }\n\n  private store(bytes: Uint8Array, value: string) {\n    const records = this.caches[bytes.length - 1]!;\n    const record: KeyCacheRecord = { bytes, str: value };\n\n    if (records.length >= this.maxLengthPerKey) {\n      // `records` are full!\n      // Set `record` to an arbitrary position.\n      records[(Math.random() * records.length) | 0] = record;\n    } else {\n      records.push(record);\n    }\n  }\n\n  public decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n    const cachedValue = this.find(bytes, inputOffset, byteLength);\n    if (cachedValue != null) {\n      this.hit++;\n      return cachedValue;\n    }\n    this.miss++;\n\n    const str = utf8DecodeJs(bytes, inputOffset, byteLength);\n    // Ensure to copy a slice of bytes because the byte may be NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n    const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n    this.store(slicedCopyOfBytes, str);\n    return str;\n  }\n}\n", "import { prettyByte } from \"./utils/prettyByte\";\nimport { ExtensionCodec, ExtensionCodecType } from \"./ExtensionCodec\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int\";\nimport { utf8DecodeJs, TEXT_DECODER_THRESHOLD, utf8DecodeTD } from \"./utils/utf8\";\nimport { createDataView, ensureUint8Array } from \"./utils/typedArrays\";\nimport { CachedKeyDecoder, KeyDecoder } from \"./CachedKeyDecoder\";\nimport { DecodeError } from \"./DecodeError\";\n\nconst enum State {\n  ARRAY,\n  MAP_KEY,\n  MAP_VALUE,\n}\n\ntype MapKeyType = string | number;\n\nconst isValidMapKeyType = (key: unknown): key is MapKeyType => {\n  const keyType = typeof key;\n\n  return keyType === \"string\" || keyType === \"number\";\n};\n\ntype StackMapState = {\n  type: State.MAP_KEY | State.MAP_VALUE;\n  size: number;\n  key: MapKeyType | null;\n  readCount: number;\n  map: Record<string, unknown>;\n};\n\ntype StackArrayState = {\n  type: State.ARRAY;\n  size: number;\n  array: Array<unknown>;\n  position: number;\n};\n\ntype StackState = StackArrayState | StackMapState;\n\nconst HEAD_BYTE_REQUIRED = -1;\n\nconst EMPTY_VIEW = new DataView(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);\n\n// IE11: Hack to support IE11.\n// IE11: Drop this hack and just use RangeError when IE11 is obsolete.\nexport const DataViewIndexOutOfBoundsError: typeof Error = (() => {\n  try {\n    // IE11: The spec says it should throw RangeError,\n    // IE11: but in IE11 it throws TypeError.\n    EMPTY_VIEW.getInt8(0);\n  } catch (e: any) {\n    return e.constructor;\n  }\n  throw new Error(\"never reached\");\n})();\n\nconst MORE_DATA = new DataViewIndexOutOfBoundsError(\"Insufficient data\");\n\nconst sharedCachedKeyDecoder = new CachedKeyDecoder();\n\nexport class Decoder<ContextType = undefined> {\n  private totalPos = 0;\n  private pos = 0;\n\n  private view = EMPTY_VIEW;\n  private bytes = EMPTY_BYTES;\n  private headByte = HEAD_BYTE_REQUIRED;\n  private readonly stack: Array<StackState> = [];\n\n  public constructor(\n    private readonly extensionCodec: ExtensionCodecType<ContextType> = ExtensionCodec.defaultCodec as any,\n    private readonly context: ContextType = undefined as any,\n    private readonly maxStrLength = UINT32_MAX,\n    private readonly maxBinLength = UINT32_MAX,\n    private readonly maxArrayLength = UINT32_MAX,\n    private readonly maxMapLength = UINT32_MAX,\n    private readonly maxExtLength = UINT32_MAX,\n    private readonly keyDecoder: KeyDecoder | null = sharedCachedKeyDecoder,\n  ) {}\n\n  private reinitializeState() {\n    this.totalPos = 0;\n    this.headByte = HEAD_BYTE_REQUIRED;\n    this.stack.length = 0;\n\n    // view, bytes, and pos will be re-initialized in setBuffer()\n  }\n\n  private setBuffer(buffer: ArrayLike<number> | BufferSource): void {\n    this.bytes = ensureUint8Array(buffer);\n    this.view = createDataView(this.bytes);\n    this.pos = 0;\n  }\n\n  private appendBuffer(buffer: ArrayLike<number> | BufferSource) {\n    if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n      this.setBuffer(buffer);\n    } else {\n      const remainingData = this.bytes.subarray(this.pos);\n      const newData = ensureUint8Array(buffer);\n\n      // concat remainingData + newData\n      const newBuffer = new Uint8Array(remainingData.length + newData.length);\n      newBuffer.set(remainingData);\n      newBuffer.set(newData, remainingData.length);\n      this.setBuffer(newBuffer);\n    }\n  }\n\n  private hasRemaining(size: number) {\n    return this.view.byteLength - this.pos >= size;\n  }\n\n  private createExtraByteError(posToShow: number): Error {\n    const { view, pos } = this;\n    return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n  }\n\n  /**\n   * @throws {@link DecodeError}\n   * @throws {@link RangeError}\n   */\n  public decode(buffer: ArrayLike<number> | BufferSource): unknown {\n    this.reinitializeState();\n    this.setBuffer(buffer);\n\n    const object = this.doDecodeSync();\n    if (this.hasRemaining(1)) {\n      throw this.createExtraByteError(this.pos);\n    }\n    return object;\n  }\n\n  public *decodeMulti(buffer: ArrayLike<number> | BufferSource): Generator<unknown, void, unknown> {\n    this.reinitializeState();\n    this.setBuffer(buffer);\n\n    while (this.hasRemaining(1)) {\n      yield this.doDecodeSync();\n    }\n  }\n\n  public async decodeAsync(stream: AsyncIterable<ArrayLike<number> | BufferSource>): Promise<unknown> {\n    let decoded = false;\n    let object: unknown;\n    for await (const buffer of stream) {\n      if (decoded) {\n        throw this.createExtraByteError(this.totalPos);\n      }\n\n      this.appendBuffer(buffer);\n\n      try {\n        object = this.doDecodeSync();\n        decoded = true;\n      } catch (e) {\n        if (!(e instanceof DataViewIndexOutOfBoundsError)) {\n          throw e; // rethrow\n        }\n        // fallthrough\n      }\n      this.totalPos += this.pos;\n    }\n\n    if (decoded) {\n      if (this.hasRemaining(1)) {\n        throw this.createExtraByteError(this.totalPos);\n      }\n      return object;\n    }\n\n    const { headByte, pos, totalPos } = this;\n    throw new RangeError(\n      `Insufficient data in parsing ${prettyByte(headByte)} at ${totalPos} (${pos} in the current buffer)`,\n    );\n  }\n\n  public decodeArrayStream(\n    stream: AsyncIterable<ArrayLike<number> | BufferSource>,\n  ): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, true);\n  }\n\n  public decodeStream(stream: AsyncIterable<ArrayLike<number> | BufferSource>): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, false);\n  }\n\n  private async *decodeMultiAsync(stream: AsyncIterable<ArrayLike<number> | BufferSource>, isArray: boolean) {\n    let isArrayHeaderRequired = isArray;\n    let arrayItemsLeft = -1;\n\n    for await (const buffer of stream) {\n      if (isArray && arrayItemsLeft === 0) {\n        throw this.createExtraByteError(this.totalPos);\n      }\n\n      this.appendBuffer(buffer);\n\n      if (isArrayHeaderRequired) {\n        arrayItemsLeft = this.readArraySize();\n        isArrayHeaderRequired = false;\n        this.complete();\n      }\n\n      try {\n        while (true) {\n          yield this.doDecodeSync();\n          if (--arrayItemsLeft === 0) {\n            break;\n          }\n        }\n      } catch (e) {\n        if (!(e instanceof DataViewIndexOutOfBoundsError)) {\n          throw e; // rethrow\n        }\n        // fallthrough\n      }\n      this.totalPos += this.pos;\n    }\n  }\n\n  private doDecodeSync(): unknown {\n    DECODE: while (true) {\n      const headByte = this.readHeadByte();\n      let object: unknown;\n\n      if (headByte >= 0xe0) {\n        // negative fixint (111x xxxx) 0xe0 - 0xff\n        object = headByte - 0x100;\n      } else if (headByte < 0xc0) {\n        if (headByte < 0x80) {\n          // positive fixint (0xxx xxxx) 0x00 - 0x7f\n          object = headByte;\n        } else if (headByte < 0x90) {\n          // fixmap (1000 xxxx) 0x80 - 0x8f\n          const size = headByte - 0x80;\n          if (size !== 0) {\n            this.pushMapState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = {};\n          }\n        } else if (headByte < 0xa0) {\n          // fixarray (1001 xxxx) 0x90 - 0x9f\n          const size = headByte - 0x90;\n          if (size !== 0) {\n            this.pushArrayState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = [];\n          }\n        } else {\n          // fixstr (101x xxxx) 0xa0 - 0xbf\n          const byteLength = headByte - 0xa0;\n          object = this.decodeUtf8String(byteLength, 0);\n        }\n      } else if (headByte === 0xc0) {\n        // nil\n        object = null;\n      } else if (headByte === 0xc2) {\n        // false\n        object = false;\n      } else if (headByte === 0xc3) {\n        // true\n        object = true;\n      } else if (headByte === 0xca) {\n        // float 32\n        object = this.readF32();\n      } else if (headByte === 0xcb) {\n        // float 64\n        object = this.readF64();\n      } else if (headByte === 0xcc) {\n        // uint 8\n        object = this.readU8();\n      } else if (headByte === 0xcd) {\n        // uint 16\n        object = this.readU16();\n      } else if (headByte === 0xce) {\n        // uint 32\n        object = this.readU32();\n      } else if (headByte === 0xcf) {\n        // uint 64\n        object = this.readU64();\n      } else if (headByte === 0xd0) {\n        // int 8\n        object = this.readI8();\n      } else if (headByte === 0xd1) {\n        // int 16\n        object = this.readI16();\n      } else if (headByte === 0xd2) {\n        // int 32\n        object = this.readI32();\n      } else if (headByte === 0xd3) {\n        // int 64\n        object = this.readI64();\n      } else if (headByte === 0xd9) {\n        // str 8\n        const byteLength = this.lookU8();\n        object = this.decodeUtf8String(byteLength, 1);\n      } else if (headByte === 0xda) {\n        // str 16\n        const byteLength = this.lookU16();\n        object = this.decodeUtf8String(byteLength, 2);\n      } else if (headByte === 0xdb) {\n        // str 32\n        const byteLength = this.lookU32();\n        object = this.decodeUtf8String(byteLength, 4);\n      } else if (headByte === 0xdc) {\n        // array 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xdd) {\n        // array 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xde) {\n        // map 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xdf) {\n        // map 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xc4) {\n        // bin 8\n        const size = this.lookU8();\n        object = this.decodeBinary(size, 1);\n      } else if (headByte === 0xc5) {\n        // bin 16\n        const size = this.lookU16();\n        object = this.decodeBinary(size, 2);\n      } else if (headByte === 0xc6) {\n        // bin 32\n        const size = this.lookU32();\n        object = this.decodeBinary(size, 4);\n      } else if (headByte === 0xd4) {\n        // fixext 1\n        object = this.decodeExtension(1, 0);\n      } else if (headByte === 0xd5) {\n        // fixext 2\n        object = this.decodeExtension(2, 0);\n      } else if (headByte === 0xd6) {\n        // fixext 4\n        object = this.decodeExtension(4, 0);\n      } else if (headByte === 0xd7) {\n        // fixext 8\n        object = this.decodeExtension(8, 0);\n      } else if (headByte === 0xd8) {\n        // fixext 16\n        object = this.decodeExtension(16, 0);\n      } else if (headByte === 0xc7) {\n        // ext 8\n        const size = this.lookU8();\n        object = this.decodeExtension(size, 1);\n      } else if (headByte === 0xc8) {\n        // ext 16\n        const size = this.lookU16();\n        object = this.decodeExtension(size, 2);\n      } else if (headByte === 0xc9) {\n        // ext 32\n        const size = this.lookU32();\n        object = this.decodeExtension(size, 4);\n      } else {\n        throw new DecodeError(`Unrecognized type byte: ${prettyByte(headByte)}`);\n      }\n\n      this.complete();\n\n      const stack = this.stack;\n      while (stack.length > 0) {\n        // arrays and maps\n        const state = stack[stack.length - 1]!;\n        if (state.type === State.ARRAY) {\n          state.array[state.position] = object;\n          state.position++;\n          if (state.position === state.size) {\n            stack.pop();\n            object = state.array;\n          } else {\n            continue DECODE;\n          }\n        } else if (state.type === State.MAP_KEY) {\n          if (!isValidMapKeyType(object)) {\n            throw new DecodeError(\"The type of key must be string or number but \" + typeof object);\n          }\n          if (object === \"__proto__\") {\n            throw new DecodeError(\"The key __proto__ is not allowed\");\n          }\n\n          state.key = object;\n          state.type = State.MAP_VALUE;\n          continue DECODE;\n        } else {\n          // it must be `state.type === State.MAP_VALUE` here\n\n          state.map[state.key!] = object;\n          state.readCount++;\n\n          if (state.readCount === state.size) {\n            stack.pop();\n            object = state.map;\n          } else {\n            state.key = null;\n            state.type = State.MAP_KEY;\n            continue DECODE;\n          }\n        }\n      }\n\n      return object;\n    }\n  }\n\n  private readHeadByte(): number {\n    if (this.headByte === HEAD_BYTE_REQUIRED) {\n      this.headByte = this.readU8();\n      // console.log(\"headByte\", prettyByte(this.headByte));\n    }\n\n    return this.headByte;\n  }\n\n  private complete(): void {\n    this.headByte = HEAD_BYTE_REQUIRED;\n  }\n\n  private readArraySize(): number {\n    const headByte = this.readHeadByte();\n\n    switch (headByte) {\n      case 0xdc:\n        return this.readU16();\n      case 0xdd:\n        return this.readU32();\n      default: {\n        if (headByte < 0xa0) {\n          return headByte - 0x90;\n        } else {\n          throw new DecodeError(`Unrecognized array type byte: ${prettyByte(headByte)}`);\n        }\n      }\n    }\n  }\n\n  private pushMapState(size: number) {\n    if (size > this.maxMapLength) {\n      throw new DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n    }\n\n    this.stack.push({\n      type: State.MAP_KEY,\n      size,\n      key: null,\n      readCount: 0,\n      map: {},\n    });\n  }\n\n  private pushArrayState(size: number) {\n    if (size > this.maxArrayLength) {\n      throw new DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n    }\n\n    this.stack.push({\n      type: State.ARRAY,\n      size,\n      array: new Array<unknown>(size),\n      position: 0,\n    });\n  }\n\n  private decodeUtf8String(byteLength: number, headerOffset: number): string {\n    if (byteLength > this.maxStrLength) {\n      throw new DecodeError(\n        `Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`,\n      );\n    }\n\n    if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headerOffset;\n    let object: string;\n    if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n      object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n    } else if (byteLength > TEXT_DECODER_THRESHOLD) {\n      object = utf8DecodeTD(this.bytes, offset, byteLength);\n    } else {\n      object = utf8DecodeJs(this.bytes, offset, byteLength);\n    }\n    this.pos += headerOffset + byteLength;\n    return object;\n  }\n\n  private stateIsMapKey(): boolean {\n    if (this.stack.length > 0) {\n      const state = this.stack[this.stack.length - 1]!;\n      return state.type === State.MAP_KEY;\n    }\n    return false;\n  }\n\n  private decodeBinary(byteLength: number, headOffset: number): Uint8Array {\n    if (byteLength > this.maxBinLength) {\n      throw new DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n    }\n\n    if (!this.hasRemaining(byteLength + headOffset)) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headOffset;\n    const object = this.bytes.subarray(offset, offset + byteLength);\n    this.pos += headOffset + byteLength;\n    return object;\n  }\n\n  private decodeExtension(size: number, headOffset: number): unknown {\n    if (size > this.maxExtLength) {\n      throw new DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n    }\n\n    const extType = this.view.getInt8(this.pos + headOffset);\n    const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n    return this.extensionCodec.decode(data, extType, this.context);\n  }\n\n  private lookU8() {\n    return this.view.getUint8(this.pos);\n  }\n\n  private lookU16() {\n    return this.view.getUint16(this.pos);\n  }\n\n  private lookU32() {\n    return this.view.getUint32(this.pos);\n  }\n\n  private readU8(): number {\n    const value = this.view.getUint8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readI8(): number {\n    const value = this.view.getInt8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readU16(): number {\n    const value = this.view.getUint16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readI16(): number {\n    const value = this.view.getInt16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readU32(): number {\n    const value = this.view.getUint32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readI32(): number {\n    const value = this.view.getInt32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readU64(): number {\n    const value = getUint64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64(): number {\n    const value = getInt64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readF32() {\n    const value = this.view.getFloat32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readF64() {\n    const value = this.view.getFloat64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n}\n", "import { Decoder } from \"./Decoder\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec\";\nimport type { ContextOf, SplitUndefined } from \"./context\";\n\nexport type DecodeOptions<ContextType = undefined> = Readonly<\n  Partial<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Maximum string length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxStrLength: number;\n    /**\n     * Maximum binary length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxBinLength: number;\n    /**\n     * Maximum array length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxArrayLength: number;\n    /**\n     * Maximum map length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxMapLength: number;\n    /**\n     * Maximum extension length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxExtLength: number;\n  }>\n> &\n  ContextOf<ContextType>;\n\nexport const defaultDecodeOptions: DecodeOptions = {};\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync()}, {@link decodeStream()}, or {@link decodeArrayStream()}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decode<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): unknown {\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n  return decoder.decode(buffer);\n}\n\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream()}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMulti<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): Generator<unknown, void, unknown> {\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n  return decoder.decodeMulti(buffer);\n}\n", "// utility for whatwg streams\n\n// The living standard of whatwg streams says\n// ReadableStream is also AsyncIterable, but\n// as of June 2019, no browser implements it.\n// See https://streams.spec.whatwg.org/ for details\nexport type ReadableStreamLike<T> = AsyncIterable<T> | ReadableStream<T>;\n\nexport function isAsyncIterable<T>(object: ReadableStreamLike<T>): object is AsyncIterable<T> {\n  return (object as any)[Symbol.asyncIterator] != null;\n}\n\nfunction assertNonNull<T>(value: T | null | undefined): asserts value is T {\n  if (value == null) {\n    throw new Error(\"Assertion Failure: value must not be null nor undefined\");\n  }\n}\n\nexport async function* asyncIterableFromStream<T>(stream: ReadableStream<T>): AsyncIterable<T> {\n  const reader = stream.getReader();\n\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n      assertNonNull(value);\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nexport function ensureAsyncIterable<T>(streamLike: ReadableStreamLike<T>): AsyncIterable<T> {\n  if (isAsyncIterable(streamLike)) {\n    return streamLike;\n  } else {\n    return asyncIterableFromStream(streamLike);\n  }\n}\n", "import { Decoder } from \"./Decoder\";\nimport { ensureAsyncIterable } from \"./utils/stream\";\nimport { defaultDecodeOptions } from \"./decode\";\nimport type { ReadableStreamLike } from \"./utils/stream\";\nimport type { DecodeOptions } from \"./decode\";\nimport type { SplitUndefined } from \"./context\";\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\n export async function decodeAsync<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): Promise<unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n  return decoder.decodeAsync(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\n export function decodeArrayStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n\n  return decoder.decodeArrayStream(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMultiStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n\n  return decoder.decodeStream(stream);\n}\n\n/**\n * @deprecated Use {@link decodeMultiStream()} instead.\n */\nexport function decodeStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): AsyncGenerator<unknown, void, unknown> {\n  return decodeMultiStream(streamLike, options);\n}\n", "// Main Functions:\n\nimport { encode } from \"./encode\";\nexport { encode };\nimport type { EncodeOptions } from \"./encode\";\nexport type { EncodeOptions };\n\nimport { decode, decodeMulti } from \"./decode\";\nexport { decode, decodeMulti };\nimport type { DecodeOptions } from \"./decode\";\nexport { DecodeOptions };\n\nimport { decodeAsync, decodeArrayStream, decodeMultiStream, decodeStream } from \"./decodeAsync\";\nexport { decodeAsync, decodeArrayStream, decodeMultiStream, decodeStream };\n\nimport { Decoder, DataViewIndexOutOfBoundsError } from \"./Decoder\";\nimport { DecodeError } from \"./DecodeError\";\nexport { Decoder, DecodeError, DataViewIndexOutOfBoundsError };\n\nimport { Encoder } from \"./Encoder\";\nexport { Encoder };\n\n// Utilitiies for Extension Types:\n\nimport { ExtensionCodec } from \"./ExtensionCodec\";\nexport { ExtensionCodec };\nimport type { ExtensionCodecType, ExtensionDecoderType, ExtensionEncoderType } from \"./ExtensionCodec\";\nexport type { ExtensionCodecType, ExtensionDecoderType, ExtensionEncoderType };\nimport { ExtData } from \"./ExtData\";\nexport { ExtData };\n\nimport {\n  EXT_TIMESTAMP,\n  encodeDateToTimeSpec,\n  encodeTimeSpecToTimestamp,\n  decodeTimestampToTimeSpec,\n  encodeTimestampExtension,\n  decodeTimestampExtension,\n} from \"./timestamp\";\nexport {\n  EXT_TIMESTAMP,\n  encodeDateToTimeSpec,\n  encodeTimeSpecToTimestamp,\n  decodeTimestampToTimeSpec,\n  encodeTimestampExtension,\n  decodeTimestampExtension,\n};\n"], "names": [], "sourceRoot": ""}