{"version": 3, "file": "msgpack.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,GACvB,CATD,CASGK,MAAM,WACT,O,wBCTA,IAAIC,EAAsB,CCA1BA,EAAwB,SAASL,EAASM,GACzC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAF,EAAwB,SAASQ,EAAKC,GAAQ,OAAOL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,EAAO,ECCtGT,EAAwB,SAASL,GACX,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GACvD,G,ytBCJO,IAAMC,EAAa,WAYnB,SAASC,EAASC,EAAgBC,EAAgBJ,GACvD,IAAMK,EAAOC,KAAKC,MAAMP,EAAQ,YAC1BQ,EAAMR,EACZG,EAAKM,UAAUL,EAAQC,GACvBF,EAAKM,UAAUL,EAAS,EAAGI,EAC7B,CAEO,SAASE,EAASP,EAAgBC,GAGvC,OAAc,WAFDD,EAAKQ,SAASP,GACfD,EAAKS,UAAUR,EAAS,EAEtC,C,seCtBMS,GACgB,oBAAZC,SAA+D,WAAxB,QAAZ,EAAO,OAAPA,cAAO,IAAPA,aAAO,EAAPA,QAASC,WAAG,eAAkB,iBAC1C,oBAAhBC,aACgB,oBAAhBC,YAEF,SAASC,EAAUC,GAKxB,IAJA,IAAMC,EAAYD,EAAIE,OAElBC,EAAa,EACbC,EAAM,EACHA,EAAMH,GAAW,CACtB,IAAIpB,EAAQmB,EAAIK,WAAWD,KAE3B,GAA6B,IAAhB,WAARvB,GAIE,GAA6B,IAAhB,WAARA,GAEVsB,GAAc,MACT,CAEL,GAAItB,GAAS,OAAUA,GAAS,OAE1BuB,EAAMH,EAAW,CACnB,IAAMK,EAAQN,EAAIK,WAAWD,GACJ,QAAZ,MAARE,OACDF,EACFvB,IAAkB,KAARA,IAAkB,KAAe,KAARyB,GAAiB,M,CAOxDH,GAF2B,IAAhB,WAARtB,GAEW,EAGA,C,MAvBhBsB,G,CA2BJ,OAAOA,CACT,CA6CA,IAAMI,EAAoBb,EAA0B,IAAIG,iBAAgBW,EAC3DC,EAA0Bf,EAEhB,oBAAZC,SAA+D,WAAxB,QAAZ,EAAO,OAAPA,cAAO,IAAPA,aAAO,EAAPA,QAASC,WAAG,eAAkB,eAChE,IACA,EAHAd,EAaS4B,GAAeH,aAAiB,EAAjBA,EAAmBI,YAJ/C,SAAgCX,EAAaY,EAAoBC,GAC/DN,EAAmBI,WAAWX,EAAKY,EAAOE,SAASD,GACrD,EANA,SAA4Bb,EAAaY,EAAoBC,GAC3DD,EAAOG,IAAIR,EAAmBS,OAAOhB,GAAMa,EAC7C,EAUO,SAASI,EAAaC,EAAmBC,EAAqBhB,GAMnE,IALA,IAAIlB,EAASkC,EACPC,EAAMnC,EAASkB,EAEfkB,EAAuB,GACzBC,EAAS,GACNrC,EAASmC,GAAK,CACnB,IAAMG,EAAQL,EAAMjC,KACpB,GAAuB,IAAV,IAARsC,GAEHF,EAAMG,KAAKD,QACN,GAAuB,MAAV,IAARA,GAAwB,CAElC,IAAME,EAA2B,GAAnBP,EAAMjC,KACpBoC,EAAMG,MAAe,GAARD,IAAiB,EAAKE,E,MAC9B,GAAuB,MAAV,IAARF,GAAwB,CAE5BE,EAA2B,GAAnBP,EAAMjC,KAApB,IACMyC,EAA2B,GAAnBR,EAAMjC,KACpBoC,EAAMG,MAAe,GAARD,IAAiB,GAAOE,GAAS,EAAKC,E,MAC9C,GAAuB,MAAV,IAARH,GAAwB,CAElC,IAGII,GAAiB,EAARJ,IAAiB,IAHxBE,EAA2B,GAAnBP,EAAMjC,OAG4B,IAF1CyC,EAA2B,GAAnBR,EAAMjC,OAE8C,EADjC,GAAnBiC,EAAMjC,KAEhB0C,EAAO,QACTA,GAAQ,MACRN,EAAMG,KAAOG,IAAS,GAAM,KAAS,OACrCA,EAAO,MAAiB,KAAPA,GAEnBN,EAAMG,KAAKG,E,MAEXN,EAAMG,KAAKD,GAGTF,EAAMnB,QAtCK,OAuCboB,GAAUM,OAAOC,aAAY,MAAnBD,OAAM,OAAiBP,IAAK,IACtCA,EAAMnB,OAAS,E,CAQnB,OAJImB,EAAMnB,OAAS,IACjBoB,GAAUM,OAAOC,aAAY,MAAnBD,OAAM,OAAiBP,IAAK,KAGjCC,CACT,CAEA,I,EAAMQ,EAAoBpC,EAA0B,IAAII,YAAgB,KAC3DiC,EAA0BrC,EAEhB,oBAAZC,SAA8D,WAAvB,QAAZ,EAAO,OAAPA,cAAO,IAAPA,aAAO,EAAPA,QAASC,WAAG,eAAiB,cAC/D,IACA,EAHAd,EC9JJ,EACE,SAAqBkD,EAAuBC,GAAvB,KAAAD,KAAAA,EAAuB,KAAAC,KAAAA,CAAmB,E,mcCJjE,cACE,WAAYC,GAAZ,MACE,YAAMA,IAAQ,KAGRC,EAAsCjE,OAAOkE,OAAOC,EAAY7D,W,OACtEN,OAAOoE,eAAe,EAAMH,GAE5BjE,OAAOC,eAAe,EAAM,OAAQ,CAClCoE,cAAc,EACdnE,YAAY,EACZS,MAAOwD,EAAYG,O,CAEvB,CACF,OAdiC,OAcjC,EAdA,CAAiCC,OCIpBC,GAAiB,EAUvB,SAASC,EAA0B,G,IAwBhC3D,EAxBkC4D,EAAG,MAAEC,EAAI,OACnD,GAAID,GAAO,GAAKC,GAAQ,GAAKD,GAHH,YAG+B,CAEvD,GAAa,IAATC,GAAcD,GANM,WAMsB,CAE5C,IAAME,EAAK,IAAIC,WAAW,GAG1B,OAFM/D,EAAO,IAAIgE,SAASF,EAAGG,SACxB3D,UAAU,EAAGsD,GACXE,C,CAGP,IAAMI,EAAUN,EAAM,WAChBO,EAAe,WAANP,EAOf,OANME,EAAK,IAAIC,WAAW,IACpB/D,EAAO,IAAIgE,SAASF,EAAGG,SAExB3D,UAAU,EAAIuD,GAAQ,EAAgB,EAAVK,GAEjClE,EAAKM,UAAU,EAAG6D,GACXL,C,CAQT,OAJMA,EAAK,IAAIC,WAAW,KACpB/D,EAAO,IAAIgE,SAASF,EAAGG,SACxB3D,UAAU,EAAGuD,GAClB9D,EAASC,EAAM,EAAG4D,GACXE,CAEX,CAEO,SAASM,EAAqBC,GACnC,IAAMC,EAAOD,EAAKE,UACZX,EAAMzD,KAAKC,MAAMkE,EAAO,KACxBT,EAA4B,KAApBS,EAAa,IAANV,GAGfY,EAAYrE,KAAKC,MAAMyD,EAAO,KACpC,MAAO,CACLD,IAAKA,EAAMY,EACXX,KAAMA,EAAmB,IAAZW,EAEjB,CAEO,SAASC,EAAyBC,GACvC,OAAIA,aAAkBC,KAEbhB,EADUS,EAAqBM,IAG/B,IAEX,CAEO,SAASE,EAA0B3B,GACxC,IAAMjD,EAAO,IAAIgE,SAASf,EAAKgB,OAAQhB,EAAK4B,WAAY5B,EAAK9B,YAG7D,OAAQ8B,EAAK9B,YACX,KAAK,EAIH,MAAO,CAAEyC,IAFG5D,EAAKS,UAAU,GAEboD,KADD,GAGf,KAAK,EAEH,IAAMiB,EAAoB9E,EAAKS,UAAU,GAIzC,MAAO,CAAEmD,IAF+B,YAAP,EAApBkB,GADI9E,EAAKS,UAAU,GAGlBoD,KADDiB,IAAsB,GAGrC,KAAK,GAKH,MAAO,CAAElB,IAFGrD,EAASP,EAAM,GAEb6D,KADD7D,EAAKS,UAAU,IAG9B,QACE,MAAM,IAAI4C,EAAY,uEAAgEJ,EAAK/B,SAEjG,CAEO,SAAS6D,EAAyB9B,GACvC,IAAM+B,EAAWJ,EAA0B3B,GAC3C,OAAO,IAAI0B,KAAoB,IAAfK,EAASpB,IAAYoB,EAASnB,KAAO,IACvD,CAEO,IAAMoB,EAAqB,CAChCjC,KAAMU,EACN1B,OAAQyC,EACRS,OAAQH,GCrFV,aAgBE,aAPiB,KAAAI,gBAA+E,GAC/E,KAAAC,gBAA+E,GAG/E,KAAAC,SAAwE,GACxE,KAAAC,SAAwE,GAGvFzG,KAAK0G,SAASN,EAChB,CAgEF,OA9DS,YAAAM,SAAP,SAAgB,G,IACdvC,EAAI,OACJhB,EAAM,SACNkD,EAAM,SAMN,GAAIlC,GAAQ,EAEVnE,KAAKwG,SAASrC,GAAQhB,EACtBnD,KAAKyG,SAAStC,GAAQkC,MACjB,CAEL,IAAMM,EAAQ,EAAIxC,EAClBnE,KAAKsG,gBAAgBK,GAASxD,EAC9BnD,KAAKuG,gBAAgBI,GAASN,C,CAElC,EAEO,YAAAO,YAAP,SAAmBf,EAAiBgB,GAElC,IAAK,IAAIC,EAAI,EAAGA,EAAI9G,KAAKsG,gBAAgBjE,OAAQyE,IAE/C,GAAiB,OADXC,EAAY/G,KAAKsG,gBAAgBQ,KAGzB,OADN1C,EAAO2C,EAAUlB,EAAQgB,IAG7B,OAAO,IAAIG,GADG,EAAIF,EACO1C,GAM/B,IAAS0C,EAAI,EAAGA,EAAI9G,KAAKwG,SAASnE,OAAQyE,IAAK,CAC7C,IAAMC,EAEE3C,EADR,GAAiB,OADX2C,EAAY/G,KAAKwG,SAASM,KAGlB,OADN1C,EAAO2C,EAAUlB,EAAQgB,IAG7B,OAAO,IAAIG,EADEF,EACY1C,E,CAK/B,OAAIyB,aAAkBmB,EAEbnB,EAEF,IACT,EAEO,YAAAQ,OAAP,SAAcjC,EAAkBD,EAAc0C,GAC5C,IAAMI,EAAY9C,EAAO,EAAInE,KAAKuG,iBAAiB,EAAIpC,GAAQnE,KAAKyG,SAAStC,GAC7E,OAAI8C,EACKA,EAAU7C,EAAMD,EAAM0C,GAGtB,IAAIG,EAAQ7C,EAAMC,EAE7B,EAhFuB,EAAA8C,aAA8C,IAAIC,EAiF3E,C,CAlFA,GCrBO,SAASC,EAAiBhC,GAC/B,OAAIA,aAAkBF,WACbE,EACEiC,YAAYC,OAAOlC,GACrB,IAAIF,WAAWE,EAAOA,OAAQA,EAAOY,WAAYZ,EAAO9C,YACtD8C,aAAkBiC,YACpB,IAAInC,WAAWE,GAGfF,WAAWqC,KAAKnC,EAE3B,C,gTCFA,aAKE,WACmBoC,EACAX,EACAY,EACAC,EACAC,EACAC,EACAC,EACAC,QAPA,IAAAN,IAAAA,EAAkDL,EAAeD,mBACjE,IAAAL,IAAAA,OAAuBlE,QACvB,IAAA8E,IAAAA,EAXY,UAYZ,IAAAC,IAAAA,EAXsB,WAYtB,IAAAC,IAAAA,GAAA,QACA,IAAAC,IAAAA,GAAA,QACA,IAAAC,IAAAA,GAAA,QACA,IAAAC,IAAAA,GAAA,GAPA,KAAAN,eAAAA,EACA,KAAAX,QAAAA,EACA,KAAAY,SAAAA,EACA,KAAAC,kBAAAA,EACA,KAAAC,SAAAA,EACA,KAAAC,aAAAA,EACA,KAAAC,gBAAAA,EACA,KAAAC,oBAAAA,EAZX,KAAAvF,IAAM,EACN,KAAApB,KAAO,IAAIgE,SAAS,IAAIkC,YAAYrH,KAAK0H,oBACzC,KAAArE,MAAQ,IAAI6B,WAAWlF,KAAKmB,KAAKiE,OAWtC,CAoYL,OAlYU,YAAA2C,kBAAR,WACE/H,KAAKuC,IAAM,CACb,EAOO,YAAAyF,gBAAP,SAAuBnC,GAGrB,OAFA7F,KAAK+H,oBACL/H,KAAKiI,SAASpC,EAAQ,GACf7F,KAAKqD,MAAMJ,SAAS,EAAGjD,KAAKuC,IACrC,EAKO,YAAAY,OAAP,SAAc0C,GAGZ,OAFA7F,KAAK+H,oBACL/H,KAAKiI,SAASpC,EAAQ,GACf7F,KAAKqD,MAAM6E,MAAM,EAAGlI,KAAKuC,IAClC,EAEQ,YAAA0F,SAAR,SAAiBpC,EAAiBsC,GAChC,GAAIA,EAAQnI,KAAKyH,SACf,MAAM,IAAI7C,MAAM,oCAA6BuD,IAGjC,MAAVtC,EACF7F,KAAKoI,YACsB,kBAAXvC,EAChB7F,KAAKqI,cAAcxC,GACQ,iBAAXA,EAChB7F,KAAKsI,aAAazC,GACS,iBAAXA,EAChB7F,KAAKuI,aAAa1C,GAElB7F,KAAKwI,aAAa3C,EAAQsC,EAE9B,EAEQ,YAAAM,wBAAR,SAAgCC,GAC9B,IAAMC,EAAe3I,KAAKuC,IAAMmG,EAE5B1I,KAAKmB,KAAKmB,WAAaqG,GACzB3I,KAAK4I,aAA4B,EAAfD,EAEtB,EAEQ,YAAAC,aAAR,SAAqBC,GACnB,IAAMC,EAAY,IAAIzB,YAAYwB,GAC5BE,EAAW,IAAI7D,WAAW4D,GAC1BE,EAAU,IAAI7D,SAAS2D,GAE7BC,EAAS7F,IAAIlD,KAAKqD,OAElBrD,KAAKmB,KAAO6H,EACZhJ,KAAKqD,MAAQ0F,CACf,EAEQ,YAAAX,UAAR,WACEpI,KAAKiJ,QAAQ,IACf,EAEQ,YAAAZ,cAAR,SAAsBxC,IACL,IAAXA,EACF7F,KAAKiJ,QAAQ,KAEbjJ,KAAKiJ,QAAQ,IAEjB,EACQ,YAAAX,aAAR,SAAqBzC,GACfqD,OAAOC,cAActD,KAAY7F,KAAK8H,oBACpCjC,GAAU,EACRA,EAAS,IAEX7F,KAAKiJ,QAAQpD,GACJA,EAAS,KAElB7F,KAAKiJ,QAAQ,KACbjJ,KAAKiJ,QAAQpD,IACJA,EAAS,OAElB7F,KAAKiJ,QAAQ,KACbjJ,KAAKoJ,SAASvD,IACLA,EAAS,YAElB7F,KAAKiJ,QAAQ,KACbjJ,KAAKqJ,SAASxD,KAGd7F,KAAKiJ,QAAQ,KACbjJ,KAAKsJ,SAASzD,IAGZA,IAAW,GAEb7F,KAAKiJ,QAAQ,IAAQpD,EAAS,IACrBA,IAAW,KAEpB7F,KAAKiJ,QAAQ,KACbjJ,KAAKuJ,QAAQ1D,IACJA,IAAW,OAEpB7F,KAAKiJ,QAAQ,KACbjJ,KAAKwJ,SAAS3D,IACLA,IAAW,YAEpB7F,KAAKiJ,QAAQ,KACbjJ,KAAKyJ,SAAS5D,KAGd7F,KAAKiJ,QAAQ,KACbjJ,KAAK0J,SAAS7D,IAKd7F,KAAK4H,cAEP5H,KAAKiJ,QAAQ,KACbjJ,KAAK2J,SAAS9D,KAGd7F,KAAKiJ,QAAQ,KACbjJ,KAAK4J,SAAS/D,GAGpB,EAEQ,YAAAgE,kBAAR,SAA0BvH,GACxB,GAAIA,EAAa,GAEftC,KAAKiJ,QAAQ,IAAO3G,QACf,GAAIA,EAAa,IAEtBtC,KAAKiJ,QAAQ,KACbjJ,KAAKiJ,QAAQ3G,QACR,GAAIA,EAAa,MAEtBtC,KAAKiJ,QAAQ,KACbjJ,KAAKoJ,SAAS9G,OACT,MAAIA,EAAa,YAKtB,MAAM,IAAIsC,MAAM,2BAAoBtC,EAAU,oBAH9CtC,KAAKiJ,QAAQ,KACbjJ,KAAKqJ,SAAS/G,E,CAIlB,EAEQ,YAAAiG,aAAR,SAAqB1C,GAInB,GAFkBA,EAAOxD,OAETO,EAAwB,CACtC,IAAMN,EAAaJ,EAAU2D,GAC7B7F,KAAKyI,wBALe,EAKyBnG,GAC7CtC,KAAK6J,kBAAkBvH,GACvBO,EAAagD,EAAQ7F,KAAKqD,MAAOrD,KAAKuC,KACtCvC,KAAKuC,KAAOD,C,MAENA,EAAaJ,EAAU2D,GAC7B7F,KAAKyI,wBAXe,EAWyBnG,GAC7CtC,KAAK6J,kBAAkBvH,GN9ItB,SAAsBH,EAAaY,EAAoBC,GAI5D,IAHA,IAAMZ,EAAYD,EAAIE,OAClBjB,EAAS4B,EACTT,EAAM,EACHA,EAAMH,GAAW,CACtB,IAAIpB,EAAQmB,EAAIK,WAAWD,KAE3B,GAA6B,IAAhB,WAARvB,GAAL,CAIO,GAA6B,IAAhB,WAARA,GAEV+B,EAAO3B,KAAcJ,GAAS,EAAK,GAAQ,QACtC,CAEL,GAAIA,GAAS,OAAUA,GAAS,OAE1BuB,EAAMH,EAAW,CACnB,IAAMK,EAAQN,EAAIK,WAAWD,GACJ,QAAZ,MAARE,OACDF,EACFvB,IAAkB,KAARA,IAAkB,KAAe,KAARyB,GAAiB,M,CAK7B,IAAhB,WAARzB,IAEH+B,EAAO3B,KAAcJ,GAAS,GAAM,GAAQ,IAC5C+B,EAAO3B,KAAcJ,GAAS,EAAK,GAAQ,MAG3C+B,EAAO3B,KAAcJ,GAAS,GAAM,EAAQ,IAC5C+B,EAAO3B,KAAcJ,GAAS,GAAM,GAAQ,IAC5C+B,EAAO3B,KAAcJ,GAAS,EAAK,GAAQ,I,CAI/C+B,EAAO3B,KAAqB,GAARJ,EAAgB,G,MA9BlC+B,EAAO3B,KAAYJ,C,CAgCzB,CMsGM8I,CAAajE,EAAQ7F,KAAKqD,MAAOrD,KAAKuC,KACtCvC,KAAKuC,KAAOD,CAEhB,EAEQ,YAAAkG,aAAR,SAAqB3C,EAAiBsC,GAEpC,IAAM4B,EAAM/J,KAAKwH,eAAeZ,YAAYf,EAAQ7F,KAAK6G,SACzD,GAAW,MAAPkD,EACF/J,KAAKgK,gBAAgBD,QAChB,GAAIE,MAAMC,QAAQrE,GACvB7F,KAAKmK,YAAYtE,EAAQsC,QACpB,GAAId,YAAYC,OAAOzB,GAC5B7F,KAAKoK,aAAavE,OACb,IAAsB,iBAAXA,EAIhB,MAAM,IAAIjB,MAAM,+BAAwBvE,OAAOM,UAAU0J,SAASC,MAAMzE,KAHxE7F,KAAKuK,UAAU1E,EAAmCsC,E,CAKtD,EAEQ,YAAAiC,aAAR,SAAqBvE,GACnB,IAAM2E,EAAO3E,EAAOvD,WACpB,GAAIkI,EAAO,IAETxK,KAAKiJ,QAAQ,KACbjJ,KAAKiJ,QAAQuB,QACR,GAAIA,EAAO,MAEhBxK,KAAKiJ,QAAQ,KACbjJ,KAAKoJ,SAASoB,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAI5F,MAAM,4BAAqB4F,IAHrCxK,KAAKiJ,QAAQ,KACbjJ,KAAKqJ,SAASmB,E,CAIhB,IAAMnH,EAAQ+D,EAAiBvB,GAC/B7F,KAAKyK,SAASpH,EAChB,EAEQ,YAAA8G,YAAR,SAAoBtE,EAAwBsC,G,QACpCqC,EAAO3E,EAAOxD,OACpB,GAAImI,EAAO,GAETxK,KAAKiJ,QAAQ,IAAOuB,QACf,GAAIA,EAAO,MAEhBxK,KAAKiJ,QAAQ,KACbjJ,KAAKoJ,SAASoB,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAI5F,MAAM,2BAAoB4F,IAHpCxK,KAAKiJ,QAAQ,KACbjJ,KAAKqJ,SAASmB,E,KAIhB,IAAmB,QAAA3E,GAAM,8BAAE,CAAtB,IAAM6E,EAAI,QACb1K,KAAKiI,SAASyC,EAAMvC,EAAQ,E,mGAEhC,EAEQ,YAAAwC,sBAAR,SAA8B9E,EAAiC+E,G,QACzDC,EAAQ,E,IAEZ,IAAkB,QAAAD,GAAI,mCACAjI,IAAhBkD,EADQ,UAEVgF,G,kGAIJ,OAAOA,CACT,EAEQ,YAAAN,UAAR,SAAkB1E,EAAiCsC,G,QAC3CyC,EAAOvK,OAAOuK,KAAK/E,GACrB7F,KAAK2H,UACPiD,EAAKE,OAGP,IAAMN,EAAOxK,KAAK6H,gBAAkB7H,KAAK2K,sBAAsB9E,EAAQ+E,GAAQA,EAAKvI,OAEpF,GAAImI,EAAO,GAETxK,KAAKiJ,QAAQ,IAAOuB,QACf,GAAIA,EAAO,MAEhBxK,KAAKiJ,QAAQ,KACbjJ,KAAKoJ,SAASoB,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAI5F,MAAM,gCAAyB4F,IAHzCxK,KAAKiJ,QAAQ,KACbjJ,KAAKqJ,SAASmB,E,KAKhB,IAAkB,QAAAI,GAAI,8BAAE,CAAnB,IAAMzK,EAAG,QACNa,EAAQ6E,EAAO1F,GAEfH,KAAK6H,sBAA6BlF,IAAV3B,IAC5BhB,KAAKuI,aAAapI,GAClBH,KAAKiI,SAASjH,EAAOmH,EAAQ,G,mGAGnC,EAEQ,YAAA6B,gBAAR,SAAwBD,GACtB,IAAMS,EAAOT,EAAI3F,KAAK/B,OACtB,GAAa,IAATmI,EAEFxK,KAAKiJ,QAAQ,UACR,GAAa,IAATuB,EAETxK,KAAKiJ,QAAQ,UACR,GAAa,IAATuB,EAETxK,KAAKiJ,QAAQ,UACR,GAAa,IAATuB,EAETxK,KAAKiJ,QAAQ,UACR,GAAa,KAATuB,EAETxK,KAAKiJ,QAAQ,UACR,GAAIuB,EAAO,IAEhBxK,KAAKiJ,QAAQ,KACbjJ,KAAKiJ,QAAQuB,QACR,GAAIA,EAAO,MAEhBxK,KAAKiJ,QAAQ,KACbjJ,KAAKoJ,SAASoB,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAI5F,MAAM,sCAA+B4F,IAH/CxK,KAAKiJ,QAAQ,KACbjJ,KAAKqJ,SAASmB,E,CAIhBxK,KAAKuJ,QAAQQ,EAAI5F,MACjBnE,KAAKyK,SAASV,EAAI3F,KACpB,EAEQ,YAAA6E,QAAR,SAAgBjI,GACdhB,KAAKyI,wBAAwB,GAE7BzI,KAAKmB,KAAK4J,SAAS/K,KAAKuC,IAAKvB,GAC7BhB,KAAKuC,KACP,EAEQ,YAAAkI,SAAR,SAAiBO,GACf,IAAMR,EAAOQ,EAAO3I,OACpBrC,KAAKyI,wBAAwB+B,GAE7BxK,KAAKqD,MAAMH,IAAI8H,EAAQhL,KAAKuC,KAC5BvC,KAAKuC,KAAOiI,CACd,EAEQ,YAAAjB,QAAR,SAAgBvI,GACdhB,KAAKyI,wBAAwB,GAE7BzI,KAAKmB,KAAK8J,QAAQjL,KAAKuC,IAAKvB,GAC5BhB,KAAKuC,KACP,EAEQ,YAAA6G,SAAR,SAAiBpI,GACfhB,KAAKyI,wBAAwB,GAE7BzI,KAAKmB,KAAK+J,UAAUlL,KAAKuC,IAAKvB,GAC9BhB,KAAKuC,KAAO,CACd,EAEQ,YAAAiH,SAAR,SAAiBxI,GACfhB,KAAKyI,wBAAwB,GAE7BzI,KAAKmB,KAAKgK,SAASnL,KAAKuC,IAAKvB,GAC7BhB,KAAKuC,KAAO,CACd,EAEQ,YAAA8G,SAAR,SAAiBrI,GACfhB,KAAKyI,wBAAwB,GAE7BzI,KAAKmB,KAAKM,UAAUzB,KAAKuC,IAAKvB,GAC9BhB,KAAKuC,KAAO,CACd,EAEQ,YAAAkH,SAAR,SAAiBzI,GACfhB,KAAKyI,wBAAwB,GAE7BzI,KAAKmB,KAAKiK,SAASpL,KAAKuC,IAAKvB,GAC7BhB,KAAKuC,KAAO,CACd,EAEQ,YAAAoH,SAAR,SAAiB3I,GACfhB,KAAKyI,wBAAwB,GAC7BzI,KAAKmB,KAAKkK,WAAWrL,KAAKuC,IAAKvB,GAC/BhB,KAAKuC,KAAO,CACd,EAEQ,YAAAqH,SAAR,SAAiB5I,GACfhB,KAAKyI,wBAAwB,GAC7BzI,KAAKmB,KAAKmK,WAAWtL,KAAKuC,IAAKvB,GAC/BhB,KAAKuC,KAAO,CACd,EAEQ,YAAA+G,SAAR,SAAiBtI,GACfhB,KAAKyI,wBAAwB,GPxY1B,SAAmBtH,EAAgBC,EAAgBJ,GACxD,IAAMK,EAAOL,EAAQ,WACfQ,EAAMR,EACZG,EAAKM,UAAUL,EAAQC,GACvBF,EAAKM,UAAUL,EAAS,EAAGI,EAC7B,COqYI+J,CAAUvL,KAAKmB,KAAMnB,KAAKuC,IAAKvB,GAC/BhB,KAAKuC,KAAO,CACd,EAEQ,YAAAmH,SAAR,SAAiB1I,GACfhB,KAAKyI,wBAAwB,GAE7BvH,EAASlB,KAAKmB,KAAMnB,KAAKuC,IAAKvB,GAC9BhB,KAAKuC,KAAO,CACd,EACF,EAlZA,GCgDMiJ,EAAsC,CAAC,EAQtC,SAASrI,EACdnC,EACAyK,GAYA,YAZA,IAAAA,IAAAA,EAAsDD,GAEtC,IAAIE,EAClBD,EAAQjE,eACPiE,EAA8C5E,QAC/C4E,EAAQhE,SACRgE,EAAQ/D,kBACR+D,EAAQ9D,SACR8D,EAAQ7D,aACR6D,EAAQ5D,gBACR4D,EAAQ3D,qBAEKE,gBAAgBhH,EACjC,CChFO,SAAS2K,EAAWC,GACzB,MAAO,UAAGA,EAAO,EAAI,IAAM,GAAE,aAAKtK,KAAKuK,IAAID,GAAMvB,SAAS,IAAIyB,SAAS,EAAG,KAC5E,C,ICYA,aAKE,WAAqBC,EAAgDC,QAAhD,IAAAD,IAAAA,EAjBQ,SAiBwC,IAAAC,IAAAA,EAhBpC,IAgBZ,KAAAD,aAAAA,EAAgD,KAAAC,gBAAAA,EAJrE,KAAAC,IAAM,EACN,KAAAC,KAAO,EAMLlM,KAAKmM,OAAS,GACd,IAAK,IAAIrF,EAAI,EAAGA,EAAI9G,KAAK+L,aAAcjF,IACrC9G,KAAKmM,OAAOxI,KAAK,GAErB,CAiDF,OA/CS,YAAAyI,YAAP,SAAmB9J,GACjB,OAAOA,EAAa,GAAKA,GAActC,KAAK+L,YAC9C,EAEQ,YAAAM,KAAR,SAAahJ,EAAmBC,EAAqBhB,G,QAC7CgK,EAAUtM,KAAKmM,OAAO7J,EAAa,G,IAEzCiK,EAAY,IAAqB,M,ySAAA,CAAAD,GAAO,8BAAE,CAGxC,IAHe,IAAME,EAAM,QACrBC,EAAcD,EAAOnJ,MAElBqJ,EAAI,EAAGA,EAAIpK,EAAYoK,IAC9B,GAAID,EAAYC,KAAOrJ,EAAMC,EAAcoJ,GACzC,SAASH,EAGb,OAAOC,EAAOrK,G,mGAEhB,OAAO,IACT,EAEQ,YAAAwK,MAAR,SAActJ,EAAmBrC,GAC/B,IAAMsL,EAAUtM,KAAKmM,OAAO9I,EAAMhB,OAAS,GACrCmK,EAAyB,CAAEnJ,MAAK,EAAElB,IAAKnB,GAEzCsL,EAAQjK,QAAUrC,KAAKgM,gBAGzBM,EAAShL,KAAKsL,SAAWN,EAAQjK,OAAU,GAAKmK,EAEhDF,EAAQ3I,KAAK6I,EAEjB,EAEO,YAAAnG,OAAP,SAAchD,EAAmBC,EAAqBhB,GACpD,IAAMuK,EAAc7M,KAAKqM,KAAKhJ,EAAOC,EAAahB,GAClD,GAAmB,MAAfuK,EAEF,OADA7M,KAAKiM,MACEY,EAET7M,KAAKkM,OAEL,IAAM/J,EAAMiB,EAAaC,EAAOC,EAAahB,GAEvCwK,EAAoB5H,WAAWvE,UAAUuH,MAAMrH,KAAKwC,EAAOC,EAAaA,EAAchB,GAE5F,OADAtC,KAAK2M,MAAMG,EAAmB3K,GACvBA,CACT,EACF,EA7DA,G,qpEC2BM4K,EAAa,IAAI5H,SAAS,IAAIkC,YAAY,IAC1C2F,EAAc,IAAI9H,WAAW6H,EAAW3H,QAIjC6H,EAA8C,WACzD,IAGEF,EAAWG,QAAQ,E,CACnB,MAAOC,GACP,OAAOA,EAAEC,W,CAEX,MAAM,IAAIxI,MAAM,gBACjB,CAT0D,GAWrDyI,EAAY,IAAIJ,EAA8B,qBAE9CK,EAAyB,IAAIC,EAEnC,aASE,WACmB/F,EACAX,EACA2G,EACAC,EACAC,EACAC,EACAC,EACAC,QAPA,IAAArG,IAAAA,EAAkDL,EAAeD,mBACjE,IAAAL,IAAAA,OAAuBlE,QACvB,IAAA6K,IAAAA,EAAevM,QACf,IAAAwM,IAAAA,EAAexM,QACf,IAAAyM,IAAAA,EAAiBzM,QACjB,IAAA0M,IAAAA,EAAe1M,QACf,IAAA2M,IAAAA,EAAe3M,QACf,IAAA4M,IAAAA,EAAA,GAPA,KAAArG,eAAAA,EACA,KAAAX,QAAAA,EACA,KAAA2G,aAAAA,EACA,KAAAC,aAAAA,EACA,KAAAC,eAAAA,EACA,KAAAC,aAAAA,EACA,KAAAC,aAAAA,EACA,KAAAC,WAAAA,EAhBX,KAAAC,SAAW,EACX,KAAAvL,IAAM,EAEN,KAAApB,KAAO4L,EACP,KAAA1J,MAAQ2J,EACR,KAAAe,UA5BiB,EA6BR,KAAAC,MAA2B,EAWzC,CAmiBL,OAjiBU,YAAAjG,kBAAR,WACE/H,KAAK8N,SAAW,EAChB9N,KAAK+N,UA5CkB,EA6CvB/N,KAAKgO,MAAM3L,OAAS,CAGtB,EAEQ,YAAA4L,UAAR,SAAkB7I,GAChBpF,KAAKqD,MAAQ+D,EAAiBhC,GAC9BpF,KAAKmB,KL9EF,SAAwBiE,GAC7B,GAAIA,aAAkBiC,YACpB,OAAO,IAAIlC,SAASC,GAGtB,IAAM8I,EAAa9G,EAAiBhC,GACpC,OAAO,IAAID,SAAS+I,EAAW9I,OAAQ8I,EAAWlI,WAAYkI,EAAW5L,WAC3E,CKuEgB6L,CAAenO,KAAKqD,OAChCrD,KAAKuC,IAAM,CACb,EAEQ,YAAA6L,aAAR,SAAqBhJ,GACnB,IAzDuB,IAyDnBpF,KAAK+N,UAAoC/N,KAAKqO,aAAa,GAExD,CACL,IAAMC,EAAgBtO,KAAKqD,MAAMJ,SAASjD,KAAKuC,KACzCgM,EAAUnH,EAAiBhC,GAG3B0D,EAAY,IAAI5D,WAAWoJ,EAAcjM,OAASkM,EAAQlM,QAChEyG,EAAU5F,IAAIoL,GACdxF,EAAU5F,IAAIqL,EAASD,EAAcjM,QACrCrC,KAAKiO,UAAUnF,E,MATf9I,KAAKiO,UAAU7I,EAWnB,EAEQ,YAAAiJ,aAAR,SAAqB7D,GACnB,OAAOxK,KAAKmB,KAAKmB,WAAatC,KAAKuC,KAAOiI,CAC5C,EAEQ,YAAAgE,qBAAR,SAA6BC,GACrB,IAAEtN,EAAcnB,KAAV,KAAEuC,EAAQvC,KAAL,IACjB,OAAO,IAAI0O,WAAW,gBAASvN,EAAKmB,WAAaC,EAAG,eAAOpB,EAAKmB,WAAU,oCAA4BmM,EAAS,KACjH,EAMO,YAAApI,OAAP,SAAcjB,GACZpF,KAAK+H,oBACL/H,KAAKiO,UAAU7I,GAEf,IAAMS,EAAS7F,KAAK2O,eACpB,GAAI3O,KAAKqO,aAAa,GACpB,MAAMrO,KAAKwO,qBAAqBxO,KAAKuC,KAEvC,OAAOsD,CACT,EAEQ,YAAA+I,YAAR,SAAoBxJ,G,kDAClBpF,KAAK+H,oBACL/H,KAAKiO,UAAU7I,G,wBAERpF,KAAKqO,aAAa,GACvB,GAAMrO,KAAK2O,gBADc,M,cACzB,S,4BAIS,YAAAE,YAAb,SAAyBC,G,8HACnBC,GAAU,E,yCAEa,IAAAD,G,4EACzB,GADe1J,EAAM,QACjB2J,EACF,MAAM/O,KAAKwO,qBAAqBxO,KAAK8N,UAGvC9N,KAAKoO,aAAahJ,GAElB,IACES,EAAS7F,KAAK2O,eACdI,GAAU,C,CACV,MAAO5B,GACP,KAAMA,aAAaF,GACjB,MAAME,C,CAIVnN,KAAK8N,UAAY9N,KAAKuC,I,6RAGxB,GAAIwM,EAAS,CACX,GAAI/O,KAAKqO,aAAa,GACpB,MAAMrO,KAAKwO,qBAAqBxO,KAAK8N,UAEvC,MAAO,CAAP,EAAOjI,E,CAIT,MADQkI,GAAF,EAA8B/N,MAApB,SAAEuC,EAAG,MAAEuL,EAAQ,WACzB,IAAIY,WACR,uCAAgC/C,EAAWoC,GAAS,eAAOD,EAAQ,aAAKvL,EAAG,4B,yRAIxE,YAAAyM,kBAAP,SACEF,GAEA,OAAO9O,KAAKiP,iBAAiBH,GAAQ,EACvC,EAEO,YAAAI,aAAP,SAAoBJ,GAClB,OAAO9O,KAAKiP,iBAAiBH,GAAQ,EACvC,EAEe,YAAAG,iBAAf,SAAgCH,EAAyD5E,G,4GACnFiF,EAAwBjF,EACxBkF,GAAkB,E,2CAEK,IAAAN,G,gFACzB,GADe1J,EAAM,QACjB8E,GAA8B,IAAnBkF,EACb,MAAMpP,KAAKwO,qBAAqBxO,KAAK8N,UAGvC9N,KAAKoO,aAAahJ,GAEd+J,IACFC,EAAiBpP,KAAKqP,gBACtBF,GAAwB,EACxBnP,KAAKsP,Y,oEAKGtP,KAAK2O,iB,OAAX,mB,OACA,OADA,SACyB,KAAnBS,EACJ,M,iCAIJ,M,sBAAmBnC,GACjB,MAAM,E,qBAIVjN,KAAK8N,UAAY9N,KAAKuC,I,4TAIlB,YAAAoM,aAAR,WACEY,EAAQ,OAAa,CACnB,IAAMxB,EAAW/N,KAAKwP,eAClB3J,OAAM,EAEV,GAAIkI,GAAY,IAEdlI,EAASkI,EAAW,SACf,GAAIA,EAAW,IACpB,GAAIA,EAAW,IAEblI,EAASkI,OACJ,GAAIA,EAAW,IAAM,CAG1B,GAAa,IADPvD,EAAOuD,EAAW,KACR,CACd/N,KAAKyP,aAAajF,GAClBxK,KAAKsP,WACL,SAASC,C,CAET1J,EAAS,CAAC,C,MAEP,GAAIkI,EAAW,IAAM,CAG1B,GAAa,IADPvD,EAAOuD,EAAW,KACR,CACd/N,KAAK0P,eAAelF,GACpBxK,KAAKsP,WACL,SAASC,C,CAET1J,EAAS,E,KAEN,CAEL,IAAMvD,EAAayL,EAAW,IAC9BlI,EAAS7F,KAAK2P,iBAAiBrN,EAAY,E,MAExC,GAAiB,MAAbyL,EAETlI,EAAS,UACJ,GAAiB,MAAbkI,EAETlI,GAAS,OACJ,GAAiB,MAAbkI,EAETlI,GAAS,OACJ,GAAiB,MAAbkI,EAETlI,EAAS7F,KAAK4P,eACT,GAAiB,MAAb7B,EAETlI,EAAS7F,KAAK6P,eACT,GAAiB,MAAb9B,EAETlI,EAAS7F,KAAK8P,cACT,GAAiB,MAAb/B,EAETlI,EAAS7F,KAAK+P,eACT,GAAiB,MAAbhC,EAETlI,EAAS7F,KAAKgQ,eACT,GAAiB,MAAbjC,EAETlI,EAAS7F,KAAKiQ,eACT,GAAiB,MAAblC,EAETlI,EAAS7F,KAAKkQ,cACT,GAAiB,MAAbnC,EAETlI,EAAS7F,KAAKmQ,eACT,GAAiB,MAAbpC,EAETlI,EAAS7F,KAAKoQ,eACT,GAAiB,MAAbrC,EAETlI,EAAS7F,KAAKqQ,eACT,GAAiB,MAAbtC,EAEHzL,EAAatC,KAAKsQ,SACxBzK,EAAS7F,KAAK2P,iBAAiBrN,EAAY,QACtC,GAAiB,MAAbyL,EAEHzL,EAAatC,KAAKuQ,UACxB1K,EAAS7F,KAAK2P,iBAAiBrN,EAAY,QACtC,GAAiB,MAAbyL,EAEHzL,EAAatC,KAAKwQ,UACxB3K,EAAS7F,KAAK2P,iBAAiBrN,EAAY,QACtC,GAAiB,MAAbyL,EAAmB,CAG5B,GAAa,KADPvD,EAAOxK,KAAK+P,WACF,CACd/P,KAAK0P,eAAelF,GACpBxK,KAAKsP,WACL,SAASC,C,CAET1J,EAAS,E,MAEN,GAAiB,MAAbkI,EAAmB,CAG5B,GAAa,KADPvD,EAAOxK,KAAKgQ,WACF,CACdhQ,KAAK0P,eAAelF,GACpBxK,KAAKsP,WACL,SAASC,C,CAET1J,EAAS,E,MAEN,GAAiB,MAAbkI,EAAmB,CAG5B,GAAa,KADPvD,EAAOxK,KAAK+P,WACF,CACd/P,KAAKyP,aAAajF,GAClBxK,KAAKsP,WACL,SAASC,C,CAET1J,EAAS,CAAC,C,MAEP,GAAiB,MAAbkI,EAAmB,CAG5B,GAAa,KADPvD,EAAOxK,KAAKgQ,WACF,CACdhQ,KAAKyP,aAAajF,GAClBxK,KAAKsP,WACL,SAASC,C,CAET1J,EAAS,CAAC,C,MAEP,GAAiB,MAAbkI,EAAmB,CAE5B,IAAMvD,EAAOxK,KAAKsQ,SAClBzK,EAAS7F,KAAKyQ,aAAajG,EAAM,E,MAC5B,GAAiB,MAAbuD,EAEHvD,EAAOxK,KAAKuQ,UAClB1K,EAAS7F,KAAKyQ,aAAajG,EAAM,QAC5B,GAAiB,MAAbuD,EAEHvD,EAAOxK,KAAKwQ,UAClB3K,EAAS7F,KAAKyQ,aAAajG,EAAM,QAC5B,GAAiB,MAAbuD,EAETlI,EAAS7F,KAAK0Q,gBAAgB,EAAG,QAC5B,GAAiB,MAAb3C,EAETlI,EAAS7F,KAAK0Q,gBAAgB,EAAG,QAC5B,GAAiB,MAAb3C,EAETlI,EAAS7F,KAAK0Q,gBAAgB,EAAG,QAC5B,GAAiB,MAAb3C,EAETlI,EAAS7F,KAAK0Q,gBAAgB,EAAG,QAC5B,GAAiB,MAAb3C,EAETlI,EAAS7F,KAAK0Q,gBAAgB,GAAI,QAC7B,GAAiB,MAAb3C,EAEHvD,EAAOxK,KAAKsQ,SAClBzK,EAAS7F,KAAK0Q,gBAAgBlG,EAAM,QAC/B,GAAiB,MAAbuD,EAEHvD,EAAOxK,KAAKuQ,UAClB1K,EAAS7F,KAAK0Q,gBAAgBlG,EAAM,OAC/B,IAAiB,MAAbuD,EAKT,MAAM,IAAIvJ,EAAY,kCAA2BmH,EAAWoC,KAHtDvD,EAAOxK,KAAKwQ,UAClB3K,EAAS7F,KAAK0Q,gBAAgBlG,EAAM,E,CAKtCxK,KAAKsP,WAGL,IADA,IAAMtB,EAAQhO,KAAKgO,MACZA,EAAM3L,OAAS,GAAG,CAEvB,IAAMsO,EAAQ3C,EAAMA,EAAM3L,OAAS,GACnC,GAAmB,IAAfsO,EAAMxM,KAAsB,CAG9B,GAFAwM,EAAMC,MAAMD,EAAME,UAAYhL,EAC9B8K,EAAME,WACFF,EAAME,WAAaF,EAAMnG,KAI3B,SAAS+E,EAHTvB,EAAM8C,MACNjL,EAAS8K,EAAMC,K,KAIZ,IAAmB,IAAfD,EAAMxM,KAAwB,CACvC,QAxYF4M,EAEa,WAFbA,SAwYyBlL,IAtYY,WAAZkL,EAuYrB,MAAM,IAAIvM,EAAY,uDAAyDqB,GAEjF,GAAe,cAAXA,EACF,MAAM,IAAIrB,EAAY,oCAGxBmM,EAAMxQ,IAAM0F,EACZ8K,EAAMxM,KAAO,EACb,SAASoL,C,CAOT,GAHAoB,EAAMK,IAAIL,EAAMxQ,KAAQ0F,EACxB8K,EAAMM,YAEFN,EAAMM,YAAcN,EAAMnG,KAGvB,CACLmG,EAAMxQ,IAAM,KACZwQ,EAAMxM,KAAO,EACb,SAASoL,C,CALTvB,EAAM8C,MACNjL,EAAS8K,EAAMK,G,EASrB,OAAOnL,C,CApaa,IAClBkL,CAqaN,EAEQ,YAAAvB,aAAR,WAME,OAvZuB,IAkZnBxP,KAAK+N,WACP/N,KAAK+N,SAAW/N,KAAK8P,UAIhB9P,KAAK+N,QACd,EAEQ,YAAAuB,SAAR,WACEtP,KAAK+N,UA3ZkB,CA4ZzB,EAEQ,YAAAsB,cAAR,WACE,IAAMtB,EAAW/N,KAAKwP,eAEtB,OAAQzB,GACN,KAAK,IACH,OAAO/N,KAAK+P,UACd,KAAK,IACH,OAAO/P,KAAKgQ,UACd,QACE,GAAIjC,EAAW,IACb,OAAOA,EAAW,IAElB,MAAM,IAAIvJ,EAAY,wCAAiCmH,EAAWoC,KAI1E,EAEQ,YAAA0B,aAAR,SAAqBjF,GACnB,GAAIA,EAAOxK,KAAK2N,aACd,MAAM,IAAInJ,EAAY,2CAAoCgG,EAAI,mCAA2BxK,KAAK2N,aAAY,MAG5G3N,KAAKgO,MAAMrK,KAAK,CACdQ,KAAM,EACNqG,KAAI,EACJrK,IAAK,KACL8Q,UAAW,EACXD,IAAK,CAAC,GAEV,EAEQ,YAAAtB,eAAR,SAAuBlF,GACrB,GAAIA,EAAOxK,KAAK0N,eACd,MAAM,IAAIlJ,EAAY,6CAAsCgG,EAAI,+BAAuBxK,KAAK0N,eAAc,MAG5G1N,KAAKgO,MAAMrK,KAAK,CACdQ,KAAM,EACNqG,KAAI,EACJoG,MAAO,IAAI3G,MAAeO,GAC1BqG,SAAU,GAEd,EAEQ,YAAAlB,iBAAR,SAAyBrN,EAAoB4O,G,MAC3C,GAAI5O,EAAatC,KAAKwN,aACpB,MAAM,IAAIhJ,EACR,kDAA2ClC,EAAU,6BAAqBtC,KAAKwN,aAAY,MAI/F,GAAIxN,KAAKqD,MAAMf,WAAatC,KAAKuC,IAAM2O,EAAe5O,EACpD,MAAM+K,EAGR,IACIxH,EADEzE,EAASpB,KAAKuC,IAAM2O,EAU1B,OAPErL,EADE7F,KAAKmR,kBAAkC,QAAf,EAAAnR,KAAK6N,kBAAU,eAAEzB,YAAY9J,IAC9CtC,KAAK6N,WAAWxH,OAAOrG,KAAKqD,MAAOjC,EAAQkB,GAC3CA,EAAa4B,EV3VrB,SAAsBb,EAAmBC,EAAqBhB,GACnE,IAAM8O,EAAc/N,EAAMJ,SAASK,EAAaA,EAAchB,GAC9D,OAAO2B,EAAmBoC,OAAO+K,EACnC,CUyVeC,CAAarR,KAAKqD,MAAOjC,EAAQkB,GAEjCc,EAAapD,KAAKqD,MAAOjC,EAAQkB,GAE5CtC,KAAKuC,KAAO2O,EAAe5O,EACpBuD,CACT,EAEQ,YAAAsL,cAAR,WACE,OAAInR,KAAKgO,MAAM3L,OAAS,GAEA,IADRrC,KAAKgO,MAAMhO,KAAKgO,MAAM3L,OAAS,GAChC8B,IAGjB,EAEQ,YAAAsM,aAAR,SAAqBnO,EAAoBgP,GACvC,GAAIhP,EAAatC,KAAKyN,aACpB,MAAM,IAAIjJ,EAAY,2CAAoClC,EAAU,6BAAqBtC,KAAKyN,aAAY,MAG5G,IAAKzN,KAAKqO,aAAa/L,EAAagP,GAClC,MAAMjE,EAGR,IAAMjM,EAASpB,KAAKuC,IAAM+O,EACpBzL,EAAS7F,KAAKqD,MAAMJ,SAAS7B,EAAQA,EAASkB,GAEpD,OADAtC,KAAKuC,KAAO+O,EAAahP,EAClBuD,CACT,EAEQ,YAAA6K,gBAAR,SAAwBlG,EAAc8G,GACpC,GAAI9G,EAAOxK,KAAK4N,aACd,MAAM,IAAIpJ,EAAY,2CAAoCgG,EAAI,6BAAqBxK,KAAK4N,aAAY,MAGtG,IAAM2D,EAAUvR,KAAKmB,KAAK+L,QAAQlN,KAAKuC,IAAM+O,GACvClN,EAAOpE,KAAKyQ,aAAajG,EAAM8G,EAAa,GAClD,OAAOtR,KAAKwH,eAAenB,OAAOjC,EAAMmN,EAASvR,KAAK6G,QACxD,EAEQ,YAAAyJ,OAAR,WACE,OAAOtQ,KAAKmB,KAAKqQ,SAASxR,KAAKuC,IACjC,EAEQ,YAAAgO,QAAR,WACE,OAAOvQ,KAAKmB,KAAKsQ,UAAUzR,KAAKuC,IAClC,EAEQ,YAAAiO,QAAR,WACE,OAAOxQ,KAAKmB,KAAKS,UAAU5B,KAAKuC,IAClC,EAEQ,YAAAuN,OAAR,WACE,IAAM9O,EAAQhB,KAAKmB,KAAKqQ,SAASxR,KAAKuC,KAEtC,OADAvC,KAAKuC,MACEvB,CACT,EAEQ,YAAAkP,OAAR,WACE,IAAMlP,EAAQhB,KAAKmB,KAAK+L,QAAQlN,KAAKuC,KAErC,OADAvC,KAAKuC,MACEvB,CACT,EAEQ,YAAA+O,QAAR,WACE,IAAM/O,EAAQhB,KAAKmB,KAAKsQ,UAAUzR,KAAKuC,KAEvC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAAmP,QAAR,WACE,IAAMnP,EAAQhB,KAAKmB,KAAKuQ,SAAS1R,KAAKuC,KAEtC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAAgP,QAAR,WACE,IAAMhP,EAAQhB,KAAKmB,KAAKS,UAAU5B,KAAKuC,KAEvC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAAoP,QAAR,WACE,IAAMpP,EAAQhB,KAAKmB,KAAKQ,SAAS3B,KAAKuC,KAEtC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAAiP,QAAR,WACE,IXjkBsB9O,EAAgBC,EWikBhCJ,GXjkBgBG,EWikBEnB,KAAKmB,KXjkBSC,EWikBHpB,KAAKuC,IX9jB5B,WAFDpB,EAAKS,UAAUR,GAChBD,EAAKS,UAAUR,EAAS,IWikBlC,OADApB,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAAqP,QAAR,WACE,IAAMrP,EAAQU,EAAS1B,KAAKmB,KAAMnB,KAAKuC,KAEvC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAA4O,QAAR,WACE,IAAM5O,EAAQhB,KAAKmB,KAAKwQ,WAAW3R,KAAKuC,KAExC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EAEQ,YAAA6O,QAAR,WACE,IAAM7O,EAAQhB,KAAKmB,KAAKyQ,WAAW5R,KAAKuC,KAExC,OADAvC,KAAKuC,KAAO,EACLvB,CACT,EACF,EArjBA,GCnBa6Q,EAAsC,CAAC,EAW7C,SAASxL,EACdjB,EACAqG,GAWA,YAXA,IAAAA,IAAAA,EAAsDoG,GAEtC,IAAIC,EAClBrG,EAAQjE,eACPiE,EAA8C5E,QAC/C4E,EAAQ+B,aACR/B,EAAQgC,aACRhC,EAAQiC,eACRjC,EAAQkC,aACRlC,EAAQmC,cAEKvH,OAAOjB,EACxB,CASO,SAASwJ,EACdxJ,EACAqG,GAWA,YAXA,IAAAA,IAAAA,EAAsDoG,GAEtC,IAAIC,EAClBrG,EAAQjE,eACPiE,EAA8C5E,QAC/C4E,EAAQ+B,aACR/B,EAAQgC,aACRhC,EAAQiC,eACRjC,EAAQkC,aACRlC,EAAQmC,cAEKgB,YAAYxJ,EAC7B,C,6qDC9EA,SAAS2M,EAAiB/Q,GACxB,GAAa,MAATA,EACF,MAAM,IAAI4D,MAAM,0DAEpB,CAmBO,SAASoN,GAAuBC,GACrC,OA3BgD,MA2B5BA,EA3BGnR,OAAOoR,eA4BrBD,EAnBJ,SAA2CnD,G,kGAC1CqD,EAASrD,EAAOsD,Y,yDAIM,WAAMD,EAAOE,S,cAA/B,EAAkB,SAAhBC,EAAI,OAAEtR,EAAK,QACfsR,E,cAAA,M,OACF,mB,cAEFP,EAAc/Q,G,KACRA,I,OAAN,mB,cAAA,S,wCAGFmR,EAAOI,c,6BAQAC,CAAwBP,EAEnC,CC9BQ,SAAepD,GACrBoD,EACAxG,G,YAAA,IAAAA,IAAAA,EAAsDoG,G,imCAatD,OAXM/C,EAASkD,GAAoBC,GAW5B,CAAP,EATgB,IAAIH,EAClBrG,EAAQjE,eACPiE,EAA8C5E,QAC/C4E,EAAQ+B,aACR/B,EAAQgC,aACRhC,EAAQiC,eACRjC,EAAQkC,aACRlC,EAAQmC,cAEKiB,YAAYC,G,oSAOrB,SAASE,GACfiD,EACAxG,QAAA,IAAAA,IAAAA,EAAsDoG,GAEtD,IAAM/C,EAASkD,GAAoBC,GAYnC,OAVgB,IAAIH,EAClBrG,EAAQjE,eACPiE,EAA8C5E,QAC/C4E,EAAQ+B,aACR/B,EAAQgC,aACRhC,EAAQiC,eACRjC,EAAQkC,aACRlC,EAAQmC,cAGKoB,kBAAkBF,EACnC,CAMO,SAAS2D,GACdR,EACAxG,QAAA,IAAAA,IAAAA,EAAsDoG,GAEtD,IAAM/C,EAASkD,GAAoBC,GAYnC,OAVgB,IAAIH,EAClBrG,EAAQjE,eACPiE,EAA8C5E,QAC/C4E,EAAQ+B,aACR/B,EAAQgC,aACRhC,EAAQiC,eACRjC,EAAQkC,aACRlC,EAAQmC,cAGKsB,aAAaJ,EAC9B,CAKO,SAASI,GACd+C,EACAxG,GAEA,YAFA,IAAAA,IAAAA,EAAsDoG,GAE/CY,GAAkBR,EAAYxG,EACvC,C", "sources": ["webpack://MessagePack/webpack/universalModuleDefinition", "webpack://MessagePack/webpack/bootstrap", "webpack://MessagePack/webpack/runtime/define property getters", "webpack://MessagePack/webpack/runtime/hasOwnProperty shorthand", "webpack://MessagePack/webpack/runtime/make namespace object", "webpack://MessagePack/./src/utils/int.ts", "webpack://MessagePack/./src/utils/utf8.ts", "webpack://MessagePack/./src/ExtData.ts", "webpack://MessagePack/./src/DecodeError.ts", "webpack://MessagePack/./src/timestamp.ts", "webpack://MessagePack/./src/ExtensionCodec.ts", "webpack://MessagePack/./src/utils/typedArrays.ts", "webpack://MessagePack/./src/Encoder.ts", "webpack://MessagePack/./src/encode.ts", "webpack://MessagePack/./src/utils/prettyByte.ts", "webpack://MessagePack/./src/CachedKeyDecoder.ts", "webpack://MessagePack/./src/Decoder.ts", "webpack://MessagePack/./src/decode.ts", "webpack://MessagePack/./src/utils/stream.ts", "webpack://MessagePack/./src/decodeAsync.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"MessagePack\"] = factory();\n\telse\n\t\troot[\"MessagePack\"] = factory();\n})(this, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Integer Utility\n\nexport const UINT32_MAX = 0xffff_ffff;\n\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\n\nexport function setUint64(view: DataView, offset: number, value: number): void {\n  const high = value / 0x1_0000_0000;\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function setInt64(view: DataView, offset: number, value: number): void {\n  const high = Math.floor(value / 0x1_0000_0000);\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function getInt64(view: DataView, offset: number): number {\n  const high = view.getInt32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n\nexport function getUint64(view: <PERSON>View, offset: number): number {\n  const high = view.getUint32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n", "/* eslint-disable @typescript-eslint/no-unnecessary-condition */\nimport { UINT32_MAX } from \"./int\";\n\nconst TEXT_ENCODING_AVAILABLE =\n  (typeof process === \"undefined\" || process?.env?.[\"TEXT_ENCODING\"] !== \"never\") &&\n  typeof TextEncoder !== \"undefined\" &&\n  typeof TextDecoder !== \"undefined\";\n\nexport function utf8Count(str: string): number {\n  const strLength = str.length;\n\n  let byteLength = 0;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      byteLength++;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      byteLength += 2;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        byteLength += 3;\n      } else {\n        // 4-byte\n        byteLength += 4;\n      }\n    }\n  }\n  return byteLength;\n}\n\nexport function utf8EncodeJs(str: string, output: Uint8Array, outputOffset: number): void {\n  const strLength = str.length;\n  let offset = outputOffset;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      output[offset++] = value;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      } else {\n        // 4-byte\n        output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n        output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      }\n    }\n\n    output[offset++] = (value & 0x3f) | 0x80;\n  }\n}\n\nconst sharedTextEncoder = TEXT_ENCODING_AVAILABLE ? new TextEncoder() : undefined;\nexport const TEXT_ENCODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n  ? UINT32_MAX\n  : typeof process !== \"undefined\" && process?.env?.[\"TEXT_ENCODING\"] !== \"force\"\n  ? 200\n  : 0;\n\nfunction utf8EncodeTEencode(str: string, output: Uint8Array, outputOffset: number): void {\n  output.set(sharedTextEncoder!.encode(str), outputOffset);\n}\n\nfunction utf8EncodeTEencodeInto(str: string, output: Uint8Array, outputOffset: number): void {\n  sharedTextEncoder!.encodeInto(str, output.subarray(outputOffset));\n}\n\nexport const utf8EncodeTE = sharedTextEncoder?.encodeInto ? utf8EncodeTEencodeInto : utf8EncodeTEencode;\n\nconst CHUNK_SIZE = 0x1_000;\n\nexport function utf8DecodeJs(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  let offset = inputOffset;\n  const end = offset + byteLength;\n\n  const units: Array<number> = [];\n  let result = \"\";\n  while (offset < end) {\n    const byte1 = bytes[offset++]!;\n    if ((byte1 & 0x80) === 0) {\n      // 1 byte\n      units.push(byte1);\n    } else if ((byte1 & 0xe0) === 0xc0) {\n      // 2 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 6) | byte2);\n    } else if ((byte1 & 0xf0) === 0xe0) {\n      // 3 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n    } else if ((byte1 & 0xf8) === 0xf0) {\n      // 4 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      const byte4 = bytes[offset++]! & 0x3f;\n      let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n      if (unit > 0xffff) {\n        unit -= 0x10000;\n        units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n        unit = 0xdc00 | (unit & 0x3ff);\n      }\n      units.push(unit);\n    } else {\n      units.push(byte1);\n    }\n\n    if (units.length >= CHUNK_SIZE) {\n      result += String.fromCharCode(...units);\n      units.length = 0;\n    }\n  }\n\n  if (units.length > 0) {\n    result += String.fromCharCode(...units);\n  }\n\n  return result;\n}\n\nconst sharedTextDecoder = TEXT_ENCODING_AVAILABLE ? new TextDecoder() : null;\nexport const TEXT_DECODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n  ? UINT32_MAX\n  : typeof process !== \"undefined\" && process?.env?.[\"TEXT_DECODER\"] !== \"force\"\n  ? 200\n  : 0;\n\nexport function utf8DecodeTD(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n  return sharedTextDecoder!.decode(stringBytes);\n}\n", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nexport class ExtData {\n  constructor(readonly type: number, readonly data: Uint8Array) {}\n}\n", "export class DecodeError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // fix the prototype chain in a cross-platform way\n    const proto: typeof DecodeError.prototype = Object.create(DecodeError.prototype);\n    Object.setPrototypeOf(this, proto);\n\n    Object.defineProperty(this, \"name\", {\n      configurable: true,\n      enumerable: false,\n      value: DecodeError.name,\n    });\n  }\n}\n", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError\";\nimport { getInt64, setInt64 } from \"./utils/int\";\n\nexport const EXT_TIMESTAMP = -1;\n\nexport type TimeSpec = {\n  sec: number;\n  nsec: number;\n};\n\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\n\nexport function encodeTimeSpecToTimestamp({ sec, nsec }: TimeSpec): Uint8Array {\n  if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n    // Here sec >= 0 && nsec >= 0\n    if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n      // timestamp 32 = { sec32 (unsigned) }\n      const rv = new Uint8Array(4);\n      const view = new DataView(rv.buffer);\n      view.setUint32(0, sec);\n      return rv;\n    } else {\n      // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n      const secHigh = sec / 0x100000000;\n      const secLow = sec & 0xffffffff;\n      const rv = new Uint8Array(8);\n      const view = new DataView(rv.buffer);\n      // nsec30 | secHigh2\n      view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n      // secLow32\n      view.setUint32(4, secLow);\n      return rv;\n    }\n  } else {\n    // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n    const rv = new Uint8Array(12);\n    const view = new DataView(rv.buffer);\n    view.setUint32(0, nsec);\n    setInt64(view, 4, sec);\n    return rv;\n  }\n}\n\nexport function encodeDateToTimeSpec(date: Date): TimeSpec {\n  const msec = date.getTime();\n  const sec = Math.floor(msec / 1e3);\n  const nsec = (msec - sec * 1e3) * 1e6;\n\n  // Normalizes { sec, nsec } to ensure nsec is unsigned.\n  const nsecInSec = Math.floor(nsec / 1e9);\n  return {\n    sec: sec + nsecInSec,\n    nsec: nsec - nsecInSec * 1e9,\n  };\n}\n\nexport function encodeTimestampExtension(object: unknown): Uint8Array | null {\n  if (object instanceof Date) {\n    const timeSpec = encodeDateToTimeSpec(object);\n    return encodeTimeSpecToTimestamp(timeSpec);\n  } else {\n    return null;\n  }\n}\n\nexport function decodeTimestampToTimeSpec(data: Uint8Array): TimeSpec {\n  const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n\n  // data may be 32, 64, or 96 bits\n  switch (data.byteLength) {\n    case 4: {\n      // timestamp 32 = { sec32 }\n      const sec = view.getUint32(0);\n      const nsec = 0;\n      return { sec, nsec };\n    }\n    case 8: {\n      // timestamp 64 = { nsec30, sec34 }\n      const nsec30AndSecHigh2 = view.getUint32(0);\n      const secLow32 = view.getUint32(4);\n      const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n      const nsec = nsec30AndSecHigh2 >>> 2;\n      return { sec, nsec };\n    }\n    case 12: {\n      // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n\n      const sec = getInt64(view, 4);\n      const nsec = view.getUint32(0);\n      return { sec, nsec };\n    }\n    default:\n      throw new DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n  }\n}\n\nexport function decodeTimestampExtension(data: Uint8Array): Date {\n  const timeSpec = decodeTimestampToTimeSpec(data);\n  return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\n\nexport const timestampExtension = {\n  type: EXT_TIMESTAMP,\n  encode: encodeTimestampExtension,\n  decode: decodeTimestampExtension,\n};\n", "// ExtensionCodec to handle MessagePack extensions\n\nimport { ExtData } from \"./ExtData\";\nimport { timestampExtension } from \"./timestamp\";\n\nexport type ExtensionDecoderType<ContextType> = (\n  data: Uint8Array,\n  extensionType: number,\n  context: ContextType,\n) => unknown;\n\nexport type ExtensionEncoderType<ContextType> = (input: unknown, context: ContextType) => Uint8Array | null;\n\n// immutable interface to ExtensionCodec\nexport type ExtensionCodecType<ContextType> = {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n  tryToEncode(object: unknown, context: ContextType): ExtData | null;\n  decode(data: Uint8Array, extType: number, context: ContextType): unknown;\n};\n\nexport class ExtensionCodec<ContextType = undefined> implements ExtensionCodecType<ContextType> {\n  public static readonly defaultCodec: ExtensionCodecType<undefined> = new ExtensionCodec();\n\n  // ensures ExtensionCodecType<X> matches ExtensionCodec<X>\n  // this will make type errors a lot more clear\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n\n  // built-in extensions\n  private readonly builtInEncoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly builtInDecoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  // custom extensions\n  private readonly encoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly decoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  public constructor() {\n    this.register(timestampExtension);\n  }\n\n  public register({\n    type,\n    encode,\n    decode,\n  }: {\n    type: number;\n    encode: ExtensionEncoderType<ContextType>;\n    decode: ExtensionDecoderType<ContextType>;\n  }): void {\n    if (type >= 0) {\n      // custom extensions\n      this.encoders[type] = encode;\n      this.decoders[type] = decode;\n    } else {\n      // built-in extensions\n      const index = 1 + type;\n      this.builtInEncoders[index] = encode;\n      this.builtInDecoders[index] = decode;\n    }\n  }\n\n  public tryToEncode(object: unknown, context: ContextType): ExtData | null {\n    // built-in extensions\n    for (let i = 0; i < this.builtInEncoders.length; i++) {\n      const encodeExt = this.builtInEncoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = -1 - i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    // custom extensions\n    for (let i = 0; i < this.encoders.length; i++) {\n      const encodeExt = this.encoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    if (object instanceof ExtData) {\n      // to keep ExtData as is\n      return object;\n    }\n    return null;\n  }\n\n  public decode(data: Uint8Array, type: number, context: ContextType): unknown {\n    const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n    if (decodeExt) {\n      return decodeExt(data, type, context);\n    } else {\n      // decode() does not fail, returns ExtData instead.\n      return new ExtData(type, data);\n    }\n  }\n}\n", "export function ensureUint8Array(buffer: ArrayLike<number> | Uint8Array | ArrayBufferView | ArrayBuffer): Uint8Array {\n  if (buffer instanceof Uint8Array) {\n    return buffer;\n  } else if (ArrayBuffer.isView(buffer)) {\n    return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  } else if (buffer instanceof ArrayBuffer) {\n    return new Uint8Array(buffer);\n  } else {\n    // ArrayLike<number>\n    return Uint8Array.from(buffer);\n  }\n}\n\nexport function createDataView(buffer: ArrayLike<number> | ArrayBufferView | ArrayBuffer): DataView {\n  if (buffer instanceof ArrayBuffer) {\n    return new DataView(buffer);\n  }\n\n  const bufferView = ensureUint8Array(buffer);\n  return new DataView(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\n}\n", "import { utf8EncodeJs, utf8Count, TEXT_ENCODER_THRESHOLD, utf8EncodeTE } from \"./utils/utf8\";\nimport { ExtensionCodec, ExtensionCodecType } from \"./ExtensionCodec\";\nimport { setInt64, setUint64 } from \"./utils/int\";\nimport { ensureUint8Array } from \"./utils/typedArrays\";\nimport type { ExtData } from \"./ExtData\";\n\nexport const DEFAULT_MAX_DEPTH = 100;\nexport const DEFAULT_INITIAL_BUFFER_SIZE = 2048;\n\nexport class Encoder<ContextType = undefined> {\n  private pos = 0;\n  private view = new DataView(new ArrayBuffer(this.initialBufferSize));\n  private bytes = new Uint8Array(this.view.buffer);\n\n  public constructor(\n    private readonly extensionCodec: ExtensionCodecType<ContextType> = ExtensionCodec.defaultCodec as any,\n    private readonly context: ContextType = undefined as any,\n    private readonly maxDepth = DEFAULT_MAX_DEPTH,\n    private readonly initialBufferSize = DEFAULT_INITIAL_BUFFER_SIZE,\n    private readonly sortKeys = false,\n    private readonly forceFloat32 = false,\n    private readonly ignoreUndefined = false,\n    private readonly forceIntegerToFloat = false,\n  ) {}\n\n  private reinitializeState() {\n    this.pos = 0;\n  }\n\n  /**\n   * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n   *\n   * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n   */\n  public encodeSharedRef(object: unknown): Uint8Array {\n    this.reinitializeState();\n    this.doEncode(object, 1);\n    return this.bytes.subarray(0, this.pos);\n  }\n\n  /**\n   * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n   */\n  public encode(object: unknown): Uint8Array {\n    this.reinitializeState();\n    this.doEncode(object, 1);\n    return this.bytes.slice(0, this.pos);\n  }\n\n  private doEncode(object: unknown, depth: number): void {\n    if (depth > this.maxDepth) {\n      throw new Error(`Too deep objects in depth ${depth}`);\n    }\n\n    if (object == null) {\n      this.encodeNil();\n    } else if (typeof object === \"boolean\") {\n      this.encodeBoolean(object);\n    } else if (typeof object === \"number\") {\n      this.encodeNumber(object);\n    } else if (typeof object === \"string\") {\n      this.encodeString(object);\n    } else {\n      this.encodeObject(object, depth);\n    }\n  }\n\n  private ensureBufferSizeToWrite(sizeToWrite: number) {\n    const requiredSize = this.pos + sizeToWrite;\n\n    if (this.view.byteLength < requiredSize) {\n      this.resizeBuffer(requiredSize * 2);\n    }\n  }\n\n  private resizeBuffer(newSize: number) {\n    const newBuffer = new ArrayBuffer(newSize);\n    const newBytes = new Uint8Array(newBuffer);\n    const newView = new DataView(newBuffer);\n\n    newBytes.set(this.bytes);\n\n    this.view = newView;\n    this.bytes = newBytes;\n  }\n\n  private encodeNil() {\n    this.writeU8(0xc0);\n  }\n\n  private encodeBoolean(object: boolean) {\n    if (object === false) {\n      this.writeU8(0xc2);\n    } else {\n      this.writeU8(0xc3);\n    }\n  }\n  private encodeNumber(object: number) {\n    if (Number.isSafeInteger(object) && !this.forceIntegerToFloat) {\n      if (object >= 0) {\n        if (object < 0x80) {\n          // positive fixint\n          this.writeU8(object);\n        } else if (object < 0x100) {\n          // uint 8\n          this.writeU8(0xcc);\n          this.writeU8(object);\n        } else if (object < 0x10000) {\n          // uint 16\n          this.writeU8(0xcd);\n          this.writeU16(object);\n        } else if (object < 0x100000000) {\n          // uint 32\n          this.writeU8(0xce);\n          this.writeU32(object);\n        } else {\n          // uint 64\n          this.writeU8(0xcf);\n          this.writeU64(object);\n        }\n      } else {\n        if (object >= -0x20) {\n          // negative fixint\n          this.writeU8(0xe0 | (object + 0x20));\n        } else if (object >= -0x80) {\n          // int 8\n          this.writeU8(0xd0);\n          this.writeI8(object);\n        } else if (object >= -0x8000) {\n          // int 16\n          this.writeU8(0xd1);\n          this.writeI16(object);\n        } else if (object >= -0x80000000) {\n          // int 32\n          this.writeU8(0xd2);\n          this.writeI32(object);\n        } else {\n          // int 64\n          this.writeU8(0xd3);\n          this.writeI64(object);\n        }\n      }\n    } else {\n      // non-integer numbers\n      if (this.forceFloat32) {\n        // float 32\n        this.writeU8(0xca);\n        this.writeF32(object);\n      } else {\n        // float 64\n        this.writeU8(0xcb);\n        this.writeF64(object);\n      }\n    }\n  }\n\n  private writeStringHeader(byteLength: number) {\n    if (byteLength < 32) {\n      // fixstr\n      this.writeU8(0xa0 + byteLength);\n    } else if (byteLength < 0x100) {\n      // str 8\n      this.writeU8(0xd9);\n      this.writeU8(byteLength);\n    } else if (byteLength < 0x10000) {\n      // str 16\n      this.writeU8(0xda);\n      this.writeU16(byteLength);\n    } else if (byteLength < 0x100000000) {\n      // str 32\n      this.writeU8(0xdb);\n      this.writeU32(byteLength);\n    } else {\n      throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n    }\n  }\n\n  private encodeString(object: string) {\n    const maxHeaderSize = 1 + 4;\n    const strLength = object.length;\n\n    if (strLength > TEXT_ENCODER_THRESHOLD) {\n      const byteLength = utf8Count(object);\n      this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n      this.writeStringHeader(byteLength);\n      utf8EncodeTE(object, this.bytes, this.pos);\n      this.pos += byteLength;\n    } else {\n      const byteLength = utf8Count(object);\n      this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n      this.writeStringHeader(byteLength);\n      utf8EncodeJs(object, this.bytes, this.pos);\n      this.pos += byteLength;\n    }\n  }\n\n  private encodeObject(object: unknown, depth: number) {\n    // try to encode objects with custom codec first of non-primitives\n    const ext = this.extensionCodec.tryToEncode(object, this.context);\n    if (ext != null) {\n      this.encodeExtension(ext);\n    } else if (Array.isArray(object)) {\n      this.encodeArray(object, depth);\n    } else if (ArrayBuffer.isView(object)) {\n      this.encodeBinary(object);\n    } else if (typeof object === \"object\") {\n      this.encodeMap(object as Record<string, unknown>, depth);\n    } else {\n      // symbol, function and other special object come here unless extensionCodec handles them.\n      throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n    }\n  }\n\n  private encodeBinary(object: ArrayBufferView) {\n    const size = object.byteLength;\n    if (size < 0x100) {\n      // bin 8\n      this.writeU8(0xc4);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // bin 16\n      this.writeU8(0xc5);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // bin 32\n      this.writeU8(0xc6);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large binary: ${size}`);\n    }\n    const bytes = ensureUint8Array(object);\n    this.writeU8a(bytes);\n  }\n\n  private encodeArray(object: Array<unknown>, depth: number) {\n    const size = object.length;\n    if (size < 16) {\n      // fixarray\n      this.writeU8(0x90 + size);\n    } else if (size < 0x10000) {\n      // array 16\n      this.writeU8(0xdc);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // array 32\n      this.writeU8(0xdd);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large array: ${size}`);\n    }\n    for (const item of object) {\n      this.doEncode(item, depth + 1);\n    }\n  }\n\n  private countWithoutUndefined(object: Record<string, unknown>, keys: ReadonlyArray<string>): number {\n    let count = 0;\n\n    for (const key of keys) {\n      if (object[key] !== undefined) {\n        count++;\n      }\n    }\n\n    return count;\n  }\n\n  private encodeMap(object: Record<string, unknown>, depth: number) {\n    const keys = Object.keys(object);\n    if (this.sortKeys) {\n      keys.sort();\n    }\n\n    const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n\n    if (size < 16) {\n      // fixmap\n      this.writeU8(0x80 + size);\n    } else if (size < 0x10000) {\n      // map 16\n      this.writeU8(0xde);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // map 32\n      this.writeU8(0xdf);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large map object: ${size}`);\n    }\n\n    for (const key of keys) {\n      const value = object[key];\n\n      if (!(this.ignoreUndefined && value === undefined)) {\n        this.encodeString(key);\n        this.doEncode(value, depth + 1);\n      }\n    }\n  }\n\n  private encodeExtension(ext: ExtData) {\n    const size = ext.data.length;\n    if (size === 1) {\n      // fixext 1\n      this.writeU8(0xd4);\n    } else if (size === 2) {\n      // fixext 2\n      this.writeU8(0xd5);\n    } else if (size === 4) {\n      // fixext 4\n      this.writeU8(0xd6);\n    } else if (size === 8) {\n      // fixext 8\n      this.writeU8(0xd7);\n    } else if (size === 16) {\n      // fixext 16\n      this.writeU8(0xd8);\n    } else if (size < 0x100) {\n      // ext 8\n      this.writeU8(0xc7);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // ext 16\n      this.writeU8(0xc8);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // ext 32\n      this.writeU8(0xc9);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large extension object: ${size}`);\n    }\n    this.writeI8(ext.type);\n    this.writeU8a(ext.data);\n  }\n\n  private writeU8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setUint8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU8a(values: ArrayLike<number>) {\n    const size = values.length;\n    this.ensureBufferSizeToWrite(size);\n\n    this.bytes.set(values, this.pos);\n    this.pos += size;\n  }\n\n  private writeI8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setInt8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setUint16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeI16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setInt16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeU32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setUint32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeI32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setInt32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n    this.view.setFloat32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n    this.view.setFloat64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeU64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setUint64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeI64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setInt64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n}\n", "import { Encoder } from \"./Encoder\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec\";\nimport type { ContextOf, SplitUndefined } from \"./context\";\n\nexport type EncodeOptions<ContextType = undefined> = Partial<\n  Readonly<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * The maximum depth in nested objects and arrays.\n     *\n     * Defaults to 100.\n     */\n    maxDepth: number;\n\n    /**\n     * The initial size of the internal buffer.\n     *\n     * Defaults to 2048.\n     */\n    initialBufferSize: number;\n\n    /**\n     * If `true`, the keys of an object is sorted. In other words, the encoded\n     * binary is canonical and thus comparable to another encoded binary.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    sortKeys: boolean;\n    /**\n     * If `true`, non-integer numbers are encoded in float32, not in float64 (the default).\n     *\n     * Only use it if precisions don't matter.\n     *\n     * Defaults to `false`.\n     */\n    forceFloat32: boolean;\n\n    /**\n     * If `true`, an object property with `undefined` value are ignored.\n     * e.g. `{ foo: undefined }` will be encoded as `{}`, as `JSON.stringify()` does.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    ignoreUndefined: boolean;\n\n    /**\n     * If `true`, integer numbers are encoded as floating point numbers,\n     * with the `forceFloat32` option taken into account.\n     *\n     * Defaults to `false`.\n     */\n    forceIntegerToFloat: boolean;\n  }>\n> &\n  ContextOf<ContextType>;\n\nconst defaultEncodeOptions: EncodeOptions = {};\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nexport function encode<ContextType = undefined>(\n  value: unknown,\n  options: EncodeOptions<SplitUndefined<ContextType>> = defaultEncodeOptions as any,\n): Uint8Array {\n  const encoder = new Encoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxDepth,\n    options.initialBufferSize,\n    options.sortKeys,\n    options.forceFloat32,\n    options.ignoreUndefined,\n    options.forceIntegerToFloat,\n  );\n  return encoder.encodeSharedRef(value);\n}\n", "export function prettyByte(byte: number): string {\n  return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n", "import { utf8DecodeJs } from \"./utils/utf8\";\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\n\nexport interface KeyDecoder {\n  canBeCached(byteLength: number): boolean;\n  decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string;\n}\ninterface KeyCacheRecord {\n  readonly bytes: Uint8Array;\n  readonly str: string;\n}\n\nexport class CachedKeyDecoder implements KeyDecoder {\n  hit = 0;\n  miss = 0;\n  private readonly caches: Array<Array<KeyCacheRecord>>;\n\n  constructor(readonly maxKeyLength = DEFAULT_MAX_KEY_LENGTH, readonly maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n    // avoid `new Array(N)`, which makes a sparse array,\n    // because a sparse array is typically slower than a non-sparse array.\n    this.caches = [];\n    for (let i = 0; i < this.maxKeyLength; i++) {\n      this.caches.push([]);\n    }\n  }\n\n  public canBeCached(byteLength: number): boolean {\n    return byteLength > 0 && byteLength <= this.maxKeyLength;\n  }\n\n  private find(bytes: Uint8Array, inputOffset: number, byteLength: number): string | null {\n    const records = this.caches[byteLength - 1]!;\n\n    FIND_CHUNK: for (const record of records) {\n      const recordBytes = record.bytes;\n\n      for (let j = 0; j < byteLength; j++) {\n        if (recordBytes[j] !== bytes[inputOffset + j]) {\n          continue FIND_CHUNK;\n        }\n      }\n      return record.str;\n    }\n    return null;\n  }\n\n  private store(bytes: Uint8Array, value: string) {\n    const records = this.caches[bytes.length - 1]!;\n    const record: KeyCacheRecord = { bytes, str: value };\n\n    if (records.length >= this.maxLengthPerKey) {\n      // `records` are full!\n      // Set `record` to an arbitrary position.\n      records[(Math.random() * records.length) | 0] = record;\n    } else {\n      records.push(record);\n    }\n  }\n\n  public decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n    const cachedValue = this.find(bytes, inputOffset, byteLength);\n    if (cachedValue != null) {\n      this.hit++;\n      return cachedValue;\n    }\n    this.miss++;\n\n    const str = utf8DecodeJs(bytes, inputOffset, byteLength);\n    // Ensure to copy a slice of bytes because the byte may be NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n    const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n    this.store(slicedCopyOfBytes, str);\n    return str;\n  }\n}\n", "import { prettyByte } from \"./utils/prettyByte\";\nimport { ExtensionCodec, ExtensionCodecType } from \"./ExtensionCodec\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int\";\nimport { utf8DecodeJs, TEXT_DECODER_THRESHOLD, utf8DecodeTD } from \"./utils/utf8\";\nimport { createDataView, ensureUint8Array } from \"./utils/typedArrays\";\nimport { CachedKeyDecoder, KeyDecoder } from \"./CachedKeyDecoder\";\nimport { DecodeError } from \"./DecodeError\";\n\nconst enum State {\n  ARRAY,\n  MAP_KEY,\n  MAP_VALUE,\n}\n\ntype MapKeyType = string | number;\n\nconst isValidMapKeyType = (key: unknown): key is MapKeyType => {\n  const keyType = typeof key;\n\n  return keyType === \"string\" || keyType === \"number\";\n};\n\ntype StackMapState = {\n  type: State.MAP_KEY | State.MAP_VALUE;\n  size: number;\n  key: MapKeyType | null;\n  readCount: number;\n  map: Record<string, unknown>;\n};\n\ntype StackArrayState = {\n  type: State.ARRAY;\n  size: number;\n  array: Array<unknown>;\n  position: number;\n};\n\ntype StackState = StackArrayState | StackMapState;\n\nconst HEAD_BYTE_REQUIRED = -1;\n\nconst EMPTY_VIEW = new DataView(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);\n\n// IE11: Hack to support IE11.\n// IE11: Drop this hack and just use RangeError when IE11 is obsolete.\nexport const DataViewIndexOutOfBoundsError: typeof Error = (() => {\n  try {\n    // IE11: The spec says it should throw RangeError,\n    // IE11: but in IE11 it throws TypeError.\n    EMPTY_VIEW.getInt8(0);\n  } catch (e: any) {\n    return e.constructor;\n  }\n  throw new Error(\"never reached\");\n})();\n\nconst MORE_DATA = new DataViewIndexOutOfBoundsError(\"Insufficient data\");\n\nconst sharedCachedKeyDecoder = new CachedKeyDecoder();\n\nexport class Decoder<ContextType = undefined> {\n  private totalPos = 0;\n  private pos = 0;\n\n  private view = EMPTY_VIEW;\n  private bytes = EMPTY_BYTES;\n  private headByte = HEAD_BYTE_REQUIRED;\n  private readonly stack: Array<StackState> = [];\n\n  public constructor(\n    private readonly extensionCodec: ExtensionCodecType<ContextType> = ExtensionCodec.defaultCodec as any,\n    private readonly context: ContextType = undefined as any,\n    private readonly maxStrLength = UINT32_MAX,\n    private readonly maxBinLength = UINT32_MAX,\n    private readonly maxArrayLength = UINT32_MAX,\n    private readonly maxMapLength = UINT32_MAX,\n    private readonly maxExtLength = UINT32_MAX,\n    private readonly keyDecoder: KeyDecoder | null = sharedCachedKeyDecoder,\n  ) {}\n\n  private reinitializeState() {\n    this.totalPos = 0;\n    this.headByte = HEAD_BYTE_REQUIRED;\n    this.stack.length = 0;\n\n    // view, bytes, and pos will be re-initialized in setBuffer()\n  }\n\n  private setBuffer(buffer: ArrayLike<number> | BufferSource): void {\n    this.bytes = ensureUint8Array(buffer);\n    this.view = createDataView(this.bytes);\n    this.pos = 0;\n  }\n\n  private appendBuffer(buffer: ArrayLike<number> | BufferSource) {\n    if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n      this.setBuffer(buffer);\n    } else {\n      const remainingData = this.bytes.subarray(this.pos);\n      const newData = ensureUint8Array(buffer);\n\n      // concat remainingData + newData\n      const newBuffer = new Uint8Array(remainingData.length + newData.length);\n      newBuffer.set(remainingData);\n      newBuffer.set(newData, remainingData.length);\n      this.setBuffer(newBuffer);\n    }\n  }\n\n  private hasRemaining(size: number) {\n    return this.view.byteLength - this.pos >= size;\n  }\n\n  private createExtraByteError(posToShow: number): Error {\n    const { view, pos } = this;\n    return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n  }\n\n  /**\n   * @throws {@link DecodeError}\n   * @throws {@link RangeError}\n   */\n  public decode(buffer: ArrayLike<number> | BufferSource): unknown {\n    this.reinitializeState();\n    this.setBuffer(buffer);\n\n    const object = this.doDecodeSync();\n    if (this.hasRemaining(1)) {\n      throw this.createExtraByteError(this.pos);\n    }\n    return object;\n  }\n\n  public *decodeMulti(buffer: ArrayLike<number> | BufferSource): Generator<unknown, void, unknown> {\n    this.reinitializeState();\n    this.setBuffer(buffer);\n\n    while (this.hasRemaining(1)) {\n      yield this.doDecodeSync();\n    }\n  }\n\n  public async decodeAsync(stream: AsyncIterable<ArrayLike<number> | BufferSource>): Promise<unknown> {\n    let decoded = false;\n    let object: unknown;\n    for await (const buffer of stream) {\n      if (decoded) {\n        throw this.createExtraByteError(this.totalPos);\n      }\n\n      this.appendBuffer(buffer);\n\n      try {\n        object = this.doDecodeSync();\n        decoded = true;\n      } catch (e) {\n        if (!(e instanceof DataViewIndexOutOfBoundsError)) {\n          throw e; // rethrow\n        }\n        // fallthrough\n      }\n      this.totalPos += this.pos;\n    }\n\n    if (decoded) {\n      if (this.hasRemaining(1)) {\n        throw this.createExtraByteError(this.totalPos);\n      }\n      return object;\n    }\n\n    const { headByte, pos, totalPos } = this;\n    throw new RangeError(\n      `Insufficient data in parsing ${prettyByte(headByte)} at ${totalPos} (${pos} in the current buffer)`,\n    );\n  }\n\n  public decodeArrayStream(\n    stream: AsyncIterable<ArrayLike<number> | BufferSource>,\n  ): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, true);\n  }\n\n  public decodeStream(stream: AsyncIterable<ArrayLike<number> | BufferSource>): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, false);\n  }\n\n  private async *decodeMultiAsync(stream: AsyncIterable<ArrayLike<number> | BufferSource>, isArray: boolean) {\n    let isArrayHeaderRequired = isArray;\n    let arrayItemsLeft = -1;\n\n    for await (const buffer of stream) {\n      if (isArray && arrayItemsLeft === 0) {\n        throw this.createExtraByteError(this.totalPos);\n      }\n\n      this.appendBuffer(buffer);\n\n      if (isArrayHeaderRequired) {\n        arrayItemsLeft = this.readArraySize();\n        isArrayHeaderRequired = false;\n        this.complete();\n      }\n\n      try {\n        while (true) {\n          yield this.doDecodeSync();\n          if (--arrayItemsLeft === 0) {\n            break;\n          }\n        }\n      } catch (e) {\n        if (!(e instanceof DataViewIndexOutOfBoundsError)) {\n          throw e; // rethrow\n        }\n        // fallthrough\n      }\n      this.totalPos += this.pos;\n    }\n  }\n\n  private doDecodeSync(): unknown {\n    DECODE: while (true) {\n      const headByte = this.readHeadByte();\n      let object: unknown;\n\n      if (headByte >= 0xe0) {\n        // negative fixint (111x xxxx) 0xe0 - 0xff\n        object = headByte - 0x100;\n      } else if (headByte < 0xc0) {\n        if (headByte < 0x80) {\n          // positive fixint (0xxx xxxx) 0x00 - 0x7f\n          object = headByte;\n        } else if (headByte < 0x90) {\n          // fixmap (1000 xxxx) 0x80 - 0x8f\n          const size = headByte - 0x80;\n          if (size !== 0) {\n            this.pushMapState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = {};\n          }\n        } else if (headByte < 0xa0) {\n          // fixarray (1001 xxxx) 0x90 - 0x9f\n          const size = headByte - 0x90;\n          if (size !== 0) {\n            this.pushArrayState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = [];\n          }\n        } else {\n          // fixstr (101x xxxx) 0xa0 - 0xbf\n          const byteLength = headByte - 0xa0;\n          object = this.decodeUtf8String(byteLength, 0);\n        }\n      } else if (headByte === 0xc0) {\n        // nil\n        object = null;\n      } else if (headByte === 0xc2) {\n        // false\n        object = false;\n      } else if (headByte === 0xc3) {\n        // true\n        object = true;\n      } else if (headByte === 0xca) {\n        // float 32\n        object = this.readF32();\n      } else if (headByte === 0xcb) {\n        // float 64\n        object = this.readF64();\n      } else if (headByte === 0xcc) {\n        // uint 8\n        object = this.readU8();\n      } else if (headByte === 0xcd) {\n        // uint 16\n        object = this.readU16();\n      } else if (headByte === 0xce) {\n        // uint 32\n        object = this.readU32();\n      } else if (headByte === 0xcf) {\n        // uint 64\n        object = this.readU64();\n      } else if (headByte === 0xd0) {\n        // int 8\n        object = this.readI8();\n      } else if (headByte === 0xd1) {\n        // int 16\n        object = this.readI16();\n      } else if (headByte === 0xd2) {\n        // int 32\n        object = this.readI32();\n      } else if (headByte === 0xd3) {\n        // int 64\n        object = this.readI64();\n      } else if (headByte === 0xd9) {\n        // str 8\n        const byteLength = this.lookU8();\n        object = this.decodeUtf8String(byteLength, 1);\n      } else if (headByte === 0xda) {\n        // str 16\n        const byteLength = this.lookU16();\n        object = this.decodeUtf8String(byteLength, 2);\n      } else if (headByte === 0xdb) {\n        // str 32\n        const byteLength = this.lookU32();\n        object = this.decodeUtf8String(byteLength, 4);\n      } else if (headByte === 0xdc) {\n        // array 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xdd) {\n        // array 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xde) {\n        // map 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xdf) {\n        // map 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xc4) {\n        // bin 8\n        const size = this.lookU8();\n        object = this.decodeBinary(size, 1);\n      } else if (headByte === 0xc5) {\n        // bin 16\n        const size = this.lookU16();\n        object = this.decodeBinary(size, 2);\n      } else if (headByte === 0xc6) {\n        // bin 32\n        const size = this.lookU32();\n        object = this.decodeBinary(size, 4);\n      } else if (headByte === 0xd4) {\n        // fixext 1\n        object = this.decodeExtension(1, 0);\n      } else if (headByte === 0xd5) {\n        // fixext 2\n        object = this.decodeExtension(2, 0);\n      } else if (headByte === 0xd6) {\n        // fixext 4\n        object = this.decodeExtension(4, 0);\n      } else if (headByte === 0xd7) {\n        // fixext 8\n        object = this.decodeExtension(8, 0);\n      } else if (headByte === 0xd8) {\n        // fixext 16\n        object = this.decodeExtension(16, 0);\n      } else if (headByte === 0xc7) {\n        // ext 8\n        const size = this.lookU8();\n        object = this.decodeExtension(size, 1);\n      } else if (headByte === 0xc8) {\n        // ext 16\n        const size = this.lookU16();\n        object = this.decodeExtension(size, 2);\n      } else if (headByte === 0xc9) {\n        // ext 32\n        const size = this.lookU32();\n        object = this.decodeExtension(size, 4);\n      } else {\n        throw new DecodeError(`Unrecognized type byte: ${prettyByte(headByte)}`);\n      }\n\n      this.complete();\n\n      const stack = this.stack;\n      while (stack.length > 0) {\n        // arrays and maps\n        const state = stack[stack.length - 1]!;\n        if (state.type === State.ARRAY) {\n          state.array[state.position] = object;\n          state.position++;\n          if (state.position === state.size) {\n            stack.pop();\n            object = state.array;\n          } else {\n            continue DECODE;\n          }\n        } else if (state.type === State.MAP_KEY) {\n          if (!isValidMapKeyType(object)) {\n            throw new DecodeError(\"The type of key must be string or number but \" + typeof object);\n          }\n          if (object === \"__proto__\") {\n            throw new DecodeError(\"The key __proto__ is not allowed\");\n          }\n\n          state.key = object;\n          state.type = State.MAP_VALUE;\n          continue DECODE;\n        } else {\n          // it must be `state.type === State.MAP_VALUE` here\n\n          state.map[state.key!] = object;\n          state.readCount++;\n\n          if (state.readCount === state.size) {\n            stack.pop();\n            object = state.map;\n          } else {\n            state.key = null;\n            state.type = State.MAP_KEY;\n            continue DECODE;\n          }\n        }\n      }\n\n      return object;\n    }\n  }\n\n  private readHeadByte(): number {\n    if (this.headByte === HEAD_BYTE_REQUIRED) {\n      this.headByte = this.readU8();\n      // console.log(\"headByte\", prettyByte(this.headByte));\n    }\n\n    return this.headByte;\n  }\n\n  private complete(): void {\n    this.headByte = HEAD_BYTE_REQUIRED;\n  }\n\n  private readArraySize(): number {\n    const headByte = this.readHeadByte();\n\n    switch (headByte) {\n      case 0xdc:\n        return this.readU16();\n      case 0xdd:\n        return this.readU32();\n      default: {\n        if (headByte < 0xa0) {\n          return headByte - 0x90;\n        } else {\n          throw new DecodeError(`Unrecognized array type byte: ${prettyByte(headByte)}`);\n        }\n      }\n    }\n  }\n\n  private pushMapState(size: number) {\n    if (size > this.maxMapLength) {\n      throw new DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n    }\n\n    this.stack.push({\n      type: State.MAP_KEY,\n      size,\n      key: null,\n      readCount: 0,\n      map: {},\n    });\n  }\n\n  private pushArrayState(size: number) {\n    if (size > this.maxArrayLength) {\n      throw new DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n    }\n\n    this.stack.push({\n      type: State.ARRAY,\n      size,\n      array: new Array<unknown>(size),\n      position: 0,\n    });\n  }\n\n  private decodeUtf8String(byteLength: number, headerOffset: number): string {\n    if (byteLength > this.maxStrLength) {\n      throw new DecodeError(\n        `Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`,\n      );\n    }\n\n    if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headerOffset;\n    let object: string;\n    if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n      object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n    } else if (byteLength > TEXT_DECODER_THRESHOLD) {\n      object = utf8DecodeTD(this.bytes, offset, byteLength);\n    } else {\n      object = utf8DecodeJs(this.bytes, offset, byteLength);\n    }\n    this.pos += headerOffset + byteLength;\n    return object;\n  }\n\n  private stateIsMapKey(): boolean {\n    if (this.stack.length > 0) {\n      const state = this.stack[this.stack.length - 1]!;\n      return state.type === State.MAP_KEY;\n    }\n    return false;\n  }\n\n  private decodeBinary(byteLength: number, headOffset: number): Uint8Array {\n    if (byteLength > this.maxBinLength) {\n      throw new DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n    }\n\n    if (!this.hasRemaining(byteLength + headOffset)) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headOffset;\n    const object = this.bytes.subarray(offset, offset + byteLength);\n    this.pos += headOffset + byteLength;\n    return object;\n  }\n\n  private decodeExtension(size: number, headOffset: number): unknown {\n    if (size > this.maxExtLength) {\n      throw new DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n    }\n\n    const extType = this.view.getInt8(this.pos + headOffset);\n    const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n    return this.extensionCodec.decode(data, extType, this.context);\n  }\n\n  private lookU8() {\n    return this.view.getUint8(this.pos);\n  }\n\n  private lookU16() {\n    return this.view.getUint16(this.pos);\n  }\n\n  private lookU32() {\n    return this.view.getUint32(this.pos);\n  }\n\n  private readU8(): number {\n    const value = this.view.getUint8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readI8(): number {\n    const value = this.view.getInt8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readU16(): number {\n    const value = this.view.getUint16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readI16(): number {\n    const value = this.view.getInt16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readU32(): number {\n    const value = this.view.getUint32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readI32(): number {\n    const value = this.view.getInt32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readU64(): number {\n    const value = getUint64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64(): number {\n    const value = getInt64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readF32() {\n    const value = this.view.getFloat32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readF64() {\n    const value = this.view.getFloat64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n}\n", "import { Decoder } from \"./Decoder\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec\";\nimport type { ContextOf, SplitUndefined } from \"./context\";\n\nexport type DecodeOptions<ContextType = undefined> = Readonly<\n  Partial<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Maximum string length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxStrLength: number;\n    /**\n     * Maximum binary length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxBinLength: number;\n    /**\n     * Maximum array length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxArrayLength: number;\n    /**\n     * Maximum map length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxMapLength: number;\n    /**\n     * Maximum extension length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxExtLength: number;\n  }>\n> &\n  ContextOf<ContextType>;\n\nexport const defaultDecodeOptions: DecodeOptions = {};\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync()}, {@link decodeStream()}, or {@link decodeArrayStream()}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decode<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): unknown {\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n  return decoder.decode(buffer);\n}\n\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream()}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMulti<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): Generator<unknown, void, unknown> {\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n  return decoder.decodeMulti(buffer);\n}\n", "// utility for whatwg streams\n\n// The living standard of whatwg streams says\n// ReadableStream is also AsyncIterable, but\n// as of June 2019, no browser implements it.\n// See https://streams.spec.whatwg.org/ for details\nexport type ReadableStreamLike<T> = AsyncIterable<T> | ReadableStream<T>;\n\nexport function isAsyncIterable<T>(object: ReadableStreamLike<T>): object is AsyncIterable<T> {\n  return (object as any)[Symbol.asyncIterator] != null;\n}\n\nfunction assertNonNull<T>(value: T | null | undefined): asserts value is T {\n  if (value == null) {\n    throw new Error(\"Assertion Failure: value must not be null nor undefined\");\n  }\n}\n\nexport async function* asyncIterableFromStream<T>(stream: ReadableStream<T>): AsyncIterable<T> {\n  const reader = stream.getReader();\n\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n      assertNonNull(value);\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nexport function ensureAsyncIterable<T>(streamLike: ReadableStreamLike<T>): AsyncIterable<T> {\n  if (isAsyncIterable(streamLike)) {\n    return streamLike;\n  } else {\n    return asyncIterableFromStream(streamLike);\n  }\n}\n", "import { Decoder } from \"./Decoder\";\nimport { ensureAsyncIterable } from \"./utils/stream\";\nimport { defaultDecodeOptions } from \"./decode\";\nimport type { ReadableStreamLike } from \"./utils/stream\";\nimport type { DecodeOptions } from \"./decode\";\nimport type { SplitUndefined } from \"./context\";\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\n export async function decodeAsync<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): Promise<unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n  return decoder.decodeAsync(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\n export function decodeArrayStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n\n  return decoder.decodeArrayStream(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMultiStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n\n  const decoder = new Decoder(\n    options.extensionCodec,\n    (options as typeof options & { context: any }).context,\n    options.maxStrLength,\n    options.maxBinLength,\n    options.maxArrayLength,\n    options.maxMapLength,\n    options.maxExtLength,\n  );\n\n  return decoder.decodeStream(stream);\n}\n\n/**\n * @deprecated Use {@link decodeMultiStream()} instead.\n */\nexport function decodeStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options: DecodeOptions<SplitUndefined<ContextType>> = defaultDecodeOptions as any,\n): AsyncGenerator<unknown, void, unknown> {\n  return decodeMultiStream(streamLike, options);\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "UINT32_MAX", "setInt64", "view", "offset", "high", "Math", "floor", "low", "setUint32", "getInt64", "getInt32", "getUint32", "TEXT_ENCODING_AVAILABLE", "process", "env", "TextEncoder", "TextDecoder", "utf8Count", "str", "str<PERSON><PERSON><PERSON>", "length", "byteLength", "pos", "charCodeAt", "extra", "sharedTextEncoder", "undefined", "TEXT_ENCODER_THRESHOLD", "utf8EncodeTE", "encodeInto", "output", "outputOffset", "subarray", "set", "encode", "utf8DecodeJs", "bytes", "inputOffset", "end", "units", "result", "byte1", "push", "byte2", "byte3", "unit", "String", "fromCharCode", "sharedTextDecoder", "TEXT_DECODER_THRESHOLD", "type", "data", "message", "proto", "create", "DecodeError", "setPrototypeOf", "configurable", "name", "Error", "EXT_TIMESTAMP", "encodeTimeSpecToTimestamp", "sec", "nsec", "rv", "Uint8Array", "DataView", "buffer", "secHigh", "secLow", "encodeDateToTimeSpec", "date", "msec", "getTime", "nsecInSec", "encodeTimestampExtension", "object", "Date", "decodeTimestampToTimeSpec", "byteOffset", "nsec30AndSecHigh2", "decodeTimestampExtension", "timeSpec", "timestampExtension", "decode", "builtInEncoders", "builtInDecoders", "encoders", "decoders", "register", "index", "tryToEncode", "context", "i", "encodeExt", "ExtData", "decodeExt", "defaultCodec", "ExtensionCodec", "ensureUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "extensionCodec", "max<PERSON><PERSON><PERSON>", "initialBufferSize", "sortKeys", "forceFloat32", "ignoreUndefined", "forceIntegerToFloat", "reinitializeState", "encodeSharedRef", "doEncode", "slice", "depth", "encodeNil", "encodeBoolean", "encodeNumber", "encodeString", "encodeObject", "ensureBufferSizeToWrite", "sizeToWrite", "requiredSize", "resize<PERSON>uffer", "newSize", "new<PERSON>uffer", "newBytes", "newView", "writeU8", "Number", "isSafeInteger", "writeU16", "writeU32", "writeU64", "writeI8", "writeI16", "writeI32", "writeI64", "writeF32", "writeF64", "writeStringHeader", "utf8EncodeJs", "ext", "encodeExtension", "Array", "isArray", "encodeArray", "encodeBinary", "toString", "apply", "encodeMap", "size", "writeU8a", "item", "countWithoutUndefined", "keys", "count", "sort", "setUint8", "values", "setInt8", "setUint16", "setInt16", "setInt32", "setFloat32", "setFloat64", "setUint64", "defaultEncodeOptions", "options", "Encoder", "prettyByte", "byte", "abs", "padStart", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hit", "miss", "caches", "canBeCached", "find", "records", "FIND_CHUNK", "record", "recordBytes", "j", "store", "random", "cachedValue", "slicedCopyOfBytes", "EMPTY_VIEW", "EMPTY_BYTES", "DataViewIndexOutOfBoundsError", "getInt8", "e", "constructor", "MORE_DATA", "sharedCachedKeyDecoder", "Cached<PERSON>eyDecoder", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxExt<PERSON><PERSON><PERSON>", "keyDecoder", "totalPos", "headByte", "stack", "<PERSON><PERSON><PERSON><PERSON>", "bufferView", "createDataView", "append<PERSON><PERSON>er", "hasRemaining", "remainingData", "newData", "createExtraByteError", "posToShow", "RangeError", "doDecodeSync", "decodeMulti", "decodeAsync", "stream", "decoded", "decodeArrayStream", "decodeMultiAsync", "decodeStream", "isArrayHeaderRequired", "arrayItemsLeft", "readArraySize", "complete", "DECODE", "readHeadByte", "pushMapState", "pushArrayState", "decodeUtf8String", "readF32", "readF64", "readU8", "readU16", "readU32", "readU64", "readI8", "readI16", "readI32", "readI64", "lookU8", "lookU16", "lookU32", "decodeBinary", "decodeExtension", "state", "array", "position", "pop", "keyType", "map", "readCount", "headerOffset", "stateIsMapKey", "stringBytes", "utf8DecodeTD", "headOffset", "extType", "getUint8", "getUint16", "getInt16", "getFloat32", "getFloat64", "defaultDecodeOptions", "Decoder", "assertNonNull", "ensureAsyncIterable", "streamLike", "asyncIterator", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "releaseLock", "asyncIterableFromStream", "decodeMultiStream"], "sourceRoot": ""}