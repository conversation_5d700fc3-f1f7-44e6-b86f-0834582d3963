{"version": 3, "file": "timestamp.mjs", "sourceRoot": "", "sources": ["../src/timestamp.ts"], "names": [], "mappings": "AAAA,kFAAkF;AAClF,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAEjD,MAAM,CAAC,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC;AAOhC,IAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AACnE,IAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AAEnE,MAAM,UAAU,yBAAyB,CAAC,EAAuB;QAArB,GAAG,SAAA,EAAE,IAAI,UAAA;IACnD,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE;QACvD,6BAA6B;QAC7B,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE;YAC5C,sCAAsC;YACtC,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACvB,OAAO,EAAE,CAAC;SACX;aAAM;YACL,yDAAyD;YACzD,IAAM,OAAO,GAAG,GAAG,GAAG,WAAW,CAAC;YAClC,IAAM,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC;YAChC,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,oBAAoB;YACpB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC;YACjD,WAAW;YACX,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,OAAO,EAAE,CAAC;SACX;KACF;SAAM;QACL,uDAAuD;QACvD,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACxB,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACvB,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,IAAU;IAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACnC,IAAM,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAEtC,uDAAuD;IACvD,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACzC,OAAO;QACL,GAAG,EAAE,GAAG,GAAG,SAAS;QACpB,IAAI,EAAE,IAAI,GAAG,SAAS,GAAG,GAAG;KAC7B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,MAAe;IACtD,IAAI,MAAM,YAAY,IAAI,EAAE;QAC1B,IAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,yBAAyB,CAAC,QAAQ,CAAC,CAAC;KAC5C;SAAM;QACL,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,IAAgB;IACxD,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAEzE,iCAAiC;IACjC,QAAQ,IAAI,CAAC,UAAU,EAAE;QACvB,KAAK,CAAC,CAAC,CAAC;YACN,2BAA2B;YAC3B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAM,IAAI,GAAG,CAAC,CAAC;YACf,OAAO,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,CAAC;SACtB;QACD,KAAK,CAAC,CAAC,CAAC;YACN,mCAAmC;YACnC,IAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,IAAM,GAAG,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC;YAC/D,IAAM,IAAI,GAAG,iBAAiB,KAAK,CAAC,CAAC;YACrC,OAAO,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,CAAC;SACtB;QACD,KAAK,EAAE,CAAC,CAAC;YACP,uDAAuD;YAEvD,IAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,CAAC;SACtB;QACD;YACE,MAAM,IAAI,WAAW,CAAC,uEAAgE,IAAI,CAAC,MAAM,CAAE,CAAC,CAAC;KACxG;AACH,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,IAAgB;IACvD,IAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACjD,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC5D,CAAC;AAED,MAAM,CAAC,IAAM,kBAAkB,GAAG;IAChC,IAAI,EAAE,aAAa;IACnB,MAAM,EAAE,wBAAwB;IAChC,MAAM,EAAE,wBAAwB;CACjC,CAAC"}