# Installation
> `npm install --save @types/stack-trace`

# Summary
This package contains type definitions for stack-trace (https://github.com/felixge/node-stack-trace).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-trace.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-trace/index.d.ts)
````ts
export interface StackFrame {
    getTypeName(): string;
    getFunctionName(): string;
    getMethodName(): string;
    getFileName(): string;
    getLineNumber(): number;
    getColumnNumber(): number;
    isNative(): boolean;
    isConstructor(): boolean;
}

export declare function get(belowFn?: () => void): StackFrame[];
export declare function parse(err: Error): StackFrame[];

````

### Additional Details
 * Last updated: <PERSON><PERSON>, 07 Nov 2023 15:11:36 GMT
 * Dependencies: none

# Credits
These definitions were written by [Exceptionless](https://github.com/exceptionless).
