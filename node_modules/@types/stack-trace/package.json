{"name": "@types/stack-trace", "version": "0.0.33", "description": "TypeScript definitions for stack-trace", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-trace", "license": "MIT", "contributors": [{"name": "Exceptionless", "githubUsername": "exceptionless", "url": "https://github.com/exceptionless"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/stack-trace"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "418bcac00f32ac3e5bdaf67926dedb85af000c7c5579c98033746e8a25091010", "typeScriptVersion": "4.5"}