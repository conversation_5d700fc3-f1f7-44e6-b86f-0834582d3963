import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {registerAllResources, registerAllTools} from './Utils/registrar.js';
import logger from './log/index.js';
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";

export const URI = "https://localhost:3000";

const mcpServer = new McpServer({
    name: "cactus_ai",
    version: "1.0.0",
    capabilities: {
        resources: {},
        tools: {},
        prompts: {},
        logging: {
            level: "info" 
        }
    } 
});

export const startMcpServer = async () => {
    // Register tools and resources
    await registerAllResources(mcpServer);
    await registerAllTools(mcpServer);

    const transport = new StdioServerTransport();
    // const transport = new StreamableHTTPServerTransport({
    //     sessionIdGenerator: undefined,
    // });
    await mcpServer.connect(transport);
    logger.info("mcp server started");
    
}



startMcpServer();