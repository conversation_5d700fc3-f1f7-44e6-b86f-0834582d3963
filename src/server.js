import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {registerAllResources, registerAllTools} from './Utils/registrar.js';

export const URI = "https://localhost:3000";

const mcpServer = new McpServer({
    name: "cactus_ai",
    version: "1.0.0",
    capabilities: {
        resources: {},
        tools: {},
        prompts: {}
    } 
});

export const startMcpServer = async () => {
    // Register tools and resources
    await registerAllResources(mcpServer);
    await registerAllTools(mcpServer);

    const transport = new StdioServerTransport();
    await mcpServer.connect(transport);
    // console.info("mcp server started");
}



startMcpServer();