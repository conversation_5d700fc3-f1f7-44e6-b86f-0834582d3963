import { createLogger, format, transports } from "winston";
import { Logtail } from "@logtail/node";
import { LogtailTransport } from "@logtail/winston";

import { DateTime } from 'luxon';

const { combine, timestamp, label, printf, errors, json, prettyPrint, metadata } = format;




const testingLogger = () => {
        const myFormat = printf(({ level, message, timestamp, stack }) => {
                return `${timestamp} ${level}: ${stack || message}`;
        });

        const betterStackToken = "************************"
        const betterStackIngestingHost = "https://s1305009.eu-nbg-2.betterstackdata.com";

        const logTail = new Logtail(betterStackToken, { endpoint: betterStackIngestingHost });

        const logger = createLogger({
                level: "silly",
                format: combine(
                        label({ label: "carve-backend-testing" }),
                        errors({ stack: true }),
                        timestamp({
                                format: () =>
                                        DateTime.now().setZone('Asia/Kolkata').toFormat('dd-MM-yyyy HH:mm:ss'),
                        }),
                        json(),
                        metadata(),
                        // align(),
                        prettyPrint(),
                        // logstash()    

                ),
                transports: [
                        new transports.Console(),
                        new LogtailTransport(logTail),
                ],
        });

        // ✅ Proper exception and rejection handling
        logger.exceptions.handle(
                new transports.Console(),
                new LogtailTransport(logTail)
        );

        logger.rejections.handle(
                new transports.Console(),
                new LogtailTransport(logTail)
        );

        return logger;
}

export default testingLogger;