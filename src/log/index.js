import devLogger from "./devLogger.js";
import productionLogger from "./productionLogger.js";
import testingLogger from "./testingLogger.js";

export const NODE_ENVIRONMENTS = Object.freeze({
        DEV: "dev",
        TESTING: "testing",
        STAGING: "staging",
        PRODUCTION: "production"
})

export const NODE_ENV = process.env.NODE_ENV;

/**
 * @typedef {Object} Logger 
 * @property {(msg: string) => void} info
 * @property {(msg: string) => void} error
 * @property {(msg: string) => void} warn
 * @property {(msg: string) => void} debug
 */

/** @type {Logger} */
let logger = null;

switch (NODE_ENV) {
  case NODE_ENVIRONMENTS.DEV:
    logger = devLogger();
    break;
  case NODE_ENVIRONMENTS.TESTING:
    logger = testingLogger();
    break;
  case NODE_ENVIRONMENTS.PRODUCTION:
    logger = productionLogger();
    break;
  default:
    logger = devLogger(); // fallback
}

const overrideConsole = () => {
  console.log = (...args) => logger.info(args.join(" "));
  console.error = (...args) => logger.error(args.join(" "));
  console.warn = (...args) => logger.warn(args.join(" "));
  console.debug = (...args) => logger.debug(args.join(" "));
};

overrideConsole();

export default logger;
