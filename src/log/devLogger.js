import { createLogger, format, transports } from "winston";
const { combine, timestamp, label, printf, json, prettyPrint, colorize, align, logstash, padLevels, metadata, errors } = format;
import { DateTime } from "luxon";

const devLogger = () => {
        // const myFormat = printf(({ level, message, timestamp, stack }) => {
        //         return `${timestamp} ${level}: ${stack || message}`;
        // });

        const myFormat = printf(({ level, message, timestamp, stack }) => {
                let displayMessage = message;

                // Try parsing JSON if message is a string
                if (typeof message === 'string') {
                        try {
                                const parsed = JSON.parse(message);
                                displayMessage = JSON.stringify(parsed, null, 2); // Pretty-print JSON
                        } catch (e) {
                                // If not parsable, leave message as-is
                        }
                }

                return `${timestamp} ${level}: ${stack || displayMessage}`;
        });

        // Check if we're running as an MCP server (no console output)
        const isMcpServer = process.env.MCP_SERVER === 'true' || process.argv.includes('--mcp');

        const logger = createLogger({
                level: 'silly',
                format: combine(
                        errors({ stack: true }),
                        colorize(),
                        label({ label: 'carve-backend-dev' }),
                        timestamp({
                                format: () =>
                                        DateTime.now().setZone('Asia/Kolkata').toFormat('dd-MM-yyyy HH:mm:ss'),
                        }),
                        json(),
                        // metadata(),
                        // prettyPrint(),
                        // align(),
                        // padLevels(),
                        myFormat,

                ),
                transports: [
                        // Only use console transport if not running as MCP server
                        ...(isMcpServer ? [] : [new transports.Console()]),
                        // Always write to file for MCP server
                        new transports.File({ 
                                filename: './logs/mcp-server.log',
                                level: 'info'
                        }),
                        new transports.File({ 
                                filename: './logs/mcp-server-error.log',
                                level: 'error'
                        })
                ],
        });

        // logger.info('User login', {
        //         req_id : global.requestId ?? "----"
        // });

        return logger;
}

export default devLogger;