import { createLogger, format, transports } from "winston";
import { Logtail } from "@logtail/node";
import { LogtailTransport } from "@logtail/winston";

import { DateTime } from 'luxon';

const { combine, timestamp, label, printf, errors, json, metadata, prettyPrint } = format;




const productionLogger = () => {
        const myFormat = printf(({ level, message, timestamp, stack }) => {
                return `${timestamp} ${level}: ${stack || message}`;
        });

        const betterStackToken = "KRBRwydV25Nbp1n4NaFcFcFj85"
        const betterStackIngestingHost = "https://s1305009.eu-nbg-2.betterstackdata.com";

        const logTail = new Logtail(betterStackToken, { endpoint: betterStackIngestingHost });

        // Check if we're running as an MCP server (no console output)
        const isMcpServer = process.env.MCP_SERVER === 'true' || process.argv.includes('--mcp');

        const logger = createLogger({
                level: "silly",
                format: combine(
                        label({ label: "carve-backend-production" }),
                        errors({ stack: true }),
                        timestamp({
                                format: () =>
                                        DateTime.now().setZone('Asia/Kolkata').toFormat('dd-MM-yyyy HH:mm:ss'),
                        }),
                        json(),
                        metadata(),
                        // align(),
                        prettyPrint(),
                        // logstash()   
                ),
                transports: [
                        // Only use console transport if not running as MCP server
                        ...(isMcpServer ? [] : [new transports.Console()]),
                        new LogtailTransport(logTail),
                        // Always write to file for MCP server
                        ...(isMcpServer ? [new transports.File({ 
                                filename: './logs/mcp-server.log',
                                level: 'info'
                        })] : [])
                ],
        });

        // ✅ Proper exception and rejection handling
        if (isMcpServer) {
                logger.exceptions.handle(
                        new LogtailTransport(logTail),
                        new transports.File({ 
                                filename: './logs/mcp-server-error.log',
                                level: 'error'
                        })
                );

                logger.rejections.handle(
                        new LogtailTransport(logTail),
                        new transports.File({ 
                                filename: './logs/mcp-server-error.log',
                                level: 'error'
                        })
                );
        } else {
                logger.exceptions.handle(
                        new transports.Console(),
                        new LogtailTransport(logTail)
                );

                logger.rejections.handle(
                        new transports.Console(),
                        new LogtailTransport(logTail)
                );
        }

        return logger;
}

export default productionLogger;