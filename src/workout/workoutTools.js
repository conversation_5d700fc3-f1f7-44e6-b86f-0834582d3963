import { z } from 'zod';

export const createWorkoutTool = {
        name: "create-workout",
        config: {
                title: "Create Workout",
                description: "Create a new workout in the database",
                inputSchema: {
                        user_ref_id: z.string().describe("User reference ID"),
                        workout_name: z.string().describe("Name of the workout"),
                        date: z.string().describe("Date of the workout in YYYY-MM-DD format"),
                },
        },
        handler: (args, extra) => {
                {
                        try {
                                return {
                                        content: [
                                                {
                                                        type: "text",
                                                        text: `Workout "${args.workout_name}" created successfully for user ${args.user_ref_id} on ${args.date}`
                                                }
                                        ]
                                };
                        } catch (error) {
                                return {
                                        content: [
                                                {
                                                        type: "text",
                                                        text: "Failed to create workout"
                                                }
                                        ]
                                };
                        }
                }
        }
};
