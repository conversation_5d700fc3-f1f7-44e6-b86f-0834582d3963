import axios from 'axios';

// Define the resource handler with proper types

export const getWorkoutsResource = {
        name: "workout",
        uri: "workout://workouts",
        config: {
                title: "Workouts",
                description: "Access to workout data from the database"
        },
        handler: async (uri, extra) => {
                try {
                        const workouts = await axios.get("http://localhost:3000/workouts");
                        return {
                                contents: [
                                        {
                                                uri: uri.href,
                                                text: JSON.stringify(workouts.data, null, 2),
                                                mimeType: "application/json"
                                        }
                                ]
                        };
                } catch (error) {
                        console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",error);
                        return {
                                contents: [
                                        {
                                                uri: uri.href,
                                                text: `Error fetching workouts: ${error.message}`,
                                                mimeType: "application/json"
                                        }
                                ]
                        };
                }
        }
};

