import axios from 'axios';

// Define the resource handler with proper types


export const listWorkoutsResource = {
        name: "workout.list",
        uri: "workout://list?auth_token=user123token&count=10&offset=0&type=all",
        config: {
                title: "List Workouts",
                description: "Fetch a paginated list of workouts for a given user. Supports parameters: count (default: 50), offset (default: 0), type (default: 'all')"
        },
        handler: async (uri, extra) => {
                try {
                        // Extract and validate parameters
                        const count = parseInt(uri.searchParams.get("count")) || 50;
                        const offset = parseInt(uri.searchParams.get("offset")) || 0;
                        const type = uri.searchParams.get("type") || 'all';

                        const params = {
                                count: Math.min(Math.max(count, 1), 100), // Limit between 1-100
                                offset: Math.max(offset, 0), // Ensure non-negative
                                type: type,
                        };

                        // Get authorization from environment or extra context
                        let authToken = process.env.AUTHORIZATION;

                        // Alternative: Check if auth is provided in extra context
                        if (extra?.auth) {
                                authToken = extra.auth;
                        }

                        // Alternative: Check if there's a session or context with auth
                        if (extra?.session?.authorization) {
                                authToken = extra.session.authorization;
                        }

                        if (!authToken) {
                                return {
                                        contents: [{
                                                uri: uri.href,
                                                text: JSON.stringify({
                                                        error: "Authorization token not available",
                                                        message: "Authentication is required to fetch workouts"
                                                }, null, 2),
                                                mimeType: "application/json"
                                        }]
                                };
                        }

                        const response = await axios.get("http://localhost:6000/v1/trainee/workout", {
                                params,
                                headers: {
                                        Authorization: `zoxicorp ${authToken}`,
                                        'Content-Type': 'application/json',
                                },
                                timeout: 10000, // 10 second timeout
                        });

                        return {
                                contents: [{
                                        uri: uri.href,
                                        text: JSON.stringify({
                                                success: true,
                                                data: response.data,
                                                params: params, // Include the params used for debugging
                                                timestamp: new Date().toISOString()
                                        }, null, 2),
                                        mimeType: "application/json"
                                }]
                        };

                } catch (error) {
                        // Better error handling
                        const errorDetails = {
                                error: true,
                                message: error.message,
                                status: error.response?.status,
                                statusText: error.response?.statusText,
                                timestamp: new Date().toISOString()
                        };

                        // Don't expose sensitive information in production
                        if (process.env.NODE_ENV === 'development') {
                                errorDetails.stack = error.stack;
                                errorDetails.config = error.config;
                        }

                        return {
                                contents: [{
                                        uri: uri.href,
                                        text: JSON.stringify(errorDetails, null, 2),
                                        mimeType: "application/json"
                                }]
                        };
                }
        }
};