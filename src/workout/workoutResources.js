import axios from 'axios';
import { getHeaders } from 'node_modules/@modelcontextprotocol/sdk/server/mcp.js';

// Define the resource handler with proper types

export const getWorkoutsResource = {
        name: "workout",
        uri: "workout://workouts",
        config: {
                title: "Workouts",
                description: "Access to workout data from the database"
        },
        handler: async (uri, extra) => {
                try {
                        const workouts = await axios.get("http://localhost:3000/workouts");
                        return {
                                contents: [
                                        {
                                                uri: uri.href,
                                                text: JSON.stringify(workouts.data, null, 2),
                                                mimeType: "application/json"
                                        }
                                ]
                        };
                } catch (error) {
                        // Don't use console.error in MCP servers - it corrupts stdio communication
                        console.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", error);
                        return {
                                contents: [
                                        {
                                                uri: uri.href,
                                                text: `Error fetching workouts: ${error.message}`,
                                                mimeType: "application/json"
                                        }
                                ]
                        };
                }
        }
};



export const listWorkoutsResource = {
        name: "workout.list",
        uri: "workout://list",
        config: {
                title: "List Workouts",
                description: "Fetch a paginated list of workouts for a given user"
        },
        handler: async (uri, extra) => {
                try {
                        const authHeader1 = getHeader(ctx, "authorization");
                        const rawToken = authHeader?.replace(/^Bearer /i, "") || "";

                        const reconstructedHeader = `zoxicorp ${rawToken}`;

                        const params = {
                                count: uri.searchParams.get("count") ?? 50,
                                offset: uri.searchParams.get("offset") ?? 0,
                                type: uri.searchParams.get("type") ?? 'all',
                                search_workout_name: uri.searchParams.get("search_workout_name") ?? '',
                        };
                        console.log(">>>>>>", extra);
                        const authHeader = extra?.headers?.Authorization;
                        if (!authHeader || !authHeader.startsWith('zoxicorp ')) {
                                throw new Error("Missing or invalid Authorization header");
                        }

                        const headers = {
                                Authorization: authHeader,
                                grant_type: 'access-token',
                        };

                        const response = await axios.get("http://localhost:6000/v1/trainee/workout", {
                                params,
                                headers,
                        });

                        return {
                                contents: [
                                        {
                                                uri: uri.href,
                                                text: JSON.stringify(response.data, null, 2),
                                                mimeType: "application/json"
                                        }
                                ]
                        };

                } catch (error) {
                        return {
                                contents: [
                                        {
                                                uri: uri.href,
                                                text: `Error fetching workouts: ${error.message}`,
                                                mimeType: "application/json"
                                        }
                                ]
                        };
                }
        }
};
