{"name": "mcp-server", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "MCP_SERVER=true node src/server.js", "dev": "MCP_SERVER=true nodemon --quiet src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "server:inspect": "set DANGEROUSLY_OMIT_AUTH=true && npx @modelcontextprotocol/inspector npm run dev"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"@modelcontextprotocol/inspector": "^0.16.2", "nodemon": "^3.1.10"}, "dependencies": {"-": "^0.0.1", "@dotenvx/dotenvx": "^1.48.4", "@logtail/node": "^0.5.5", "@logtail/winston": "^0.5.5", "@modelcontextprotocol/sdk": "^1.17.0", "axios": "^1.11.0", "cors": "^2.8.5", "express": "^5.1.0", "luxon": "^3.7.1", "winston": "^3.17.0", "zod": "^3.23.8"}}